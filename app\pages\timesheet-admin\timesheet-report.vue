<template>
  <NuxtLayout>
    <TimesheetReport />
  </NuxtLayout>
</template>

<script lang="ts" setup>
import TimesheetReport from '~/features/timesheetAdmin/SummaryReport/index.vue'

definePageMeta({
  layout: 'timesheet-admin',
})

useSeoMeta({
  title: routes.adminTimesheet.timesheetReport.label,
})

useApp().definePage({
  title: routes.adminTimesheet.timesheetReport.label,
  breadcrumbs: [routes.adminTimesheet.timesheetReport],
  sub_title: 'Time Sheet Report',
})
</script>
