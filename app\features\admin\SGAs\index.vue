<template>
  <div>
    <FormFields
      class="
        mb-2 grid w-full gap-4
        lg:grid-cols-6
      "
      :options="formFields"
    />
    <Table
      :options="tableOptions"
      @pageChange="user.fetchPageChange"
      @search="user.fetchSearch"
    >
      <template #type-team="{ row }">
        <Badge
          variant="soft"
        >
          {{ row.original.team }}
        </Badge>
      </template>

      <template #actions-cell="{ row }">
        <Button
          icon="iconamoon:eye-light"
          variant="ghost"
          color="neutral"
          square
          :to="row.original.link"
        />
      </template>
    </Table>
  </div>
</template>

<script lang="ts" setup>
import { COLUMN_TYPES } from '#core/components/Table/types'
import { useUserPageLoader } from '~/loaders/admin/user'

const user = useUserPageLoader()

const route = useRoute()
const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.SEARCH,
    class: 'lg:col-span-3',
    props: {
      name: 'q',
      placeholder: 'ค้นหา SGA Name',
    },
  },
])

const tableOptions = useTable({
  options: {
    isRouteChange: true,
  },
  repo: user,
  columns: () => [
    {
      accessorKey: 'user',
      header: 'Name',
      type: COLUMN_TYPES.TEXT,
    },
    {
      accessorKey: 'date',
      header: 'Date',
      type: COLUMN_TYPES.DATE,
    },
    {
      accessorKey: 'actions',
      header: '',
      meta: {
        class: {
          th: 'text-right',
          td: 'text-right',
        },
      },
    },
  ],
})

user.fetchSetLoading()
onMounted(() => {
  user.fetchPage(Number(route.query.page || 1), route.query.q as string, {
    params: route.query,
  })
})
</script>
