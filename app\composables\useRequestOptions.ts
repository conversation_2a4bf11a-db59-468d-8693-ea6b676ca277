import type { AxiosRequestConfig } from 'axios'

export const useRequestOptions = () => {
  const config = useRuntimeConfig()

  const mock = (): Omit<AxiosRequestConfig, 'baseURL'> & {
    baseURL: string
  } => {
    return {
      baseURL: config.public.baseAPIMock || 'http://localhost:3000/api/mock',
    }
  }

  const base = (): Omit<AxiosRequestConfig, 'baseURL'> & {
    baseURL: string
  } => {
    return {
      baseURL: config.public.baseAPI,
    }
  }

  const auth = (): Omit<AxiosRequestConfig, 'baseURL'> & {
    baseURL: string
  } => {
    return {
      baseURL: config.public.baseAPI,
      headers: {
        Authorization: `Bearer ${useAuth().token.value}`,
      },
    }
  }

  return {
    base,
    mock,
    auth,
  }
}
