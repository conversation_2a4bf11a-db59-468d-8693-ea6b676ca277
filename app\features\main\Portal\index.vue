<template>
  <div class="absolute top-20 left-0 flex h-[72px] w-screen items-center border border-[#D0D5DD] bg-[#FCFCFD]">
    <Container class="flex items-center gap-4">
      <div class="grid h-12 w-12 place-items-center rounded-lg border border-gray-100">
        <Icon
          class="size-6"
          name="mingcute:announcement-line"
        />
      </div>
      <div>
        <strong>Please submit the expenses reimbursement for 16 Jun - 15 Jul 25 via HunanOS.</strong> 16 Wed 2025
      </div>
      <Button
        variant="outline"
        class="ml-auto"
        color="neutral"
        :to="routes.announcements.to"
      >
        See All
      </Button>
    </Container>
  </div>
  <div class="mt-[120px] flex-1">
    <div class="mb-12">
      <h2 class="mb-4 text-3xl font-semibold">
        My Apps
      </h2>
      <div class="grid gap-8 md:grid-cols-3">
        <div
          v-for="app in apps"
          :key="app.name"
          class="flex h-[172px] items-center justify-center rounded-lg bg-white p-6 shadow"
        >
          <Button
            :to="app.to"
            variant="link"
          >
            <img
              v-if="app.logo"
              :src="app.logo"
              :alt="app.logo"
            />
          </Button>
        </div>
      </div>
    </div>
    <div class="">
      <h2 class="mb-4 text-3xl font-semibold">
        Admin
      </h2>
      <div class="grid gap-8 md:grid-cols-3">
        <div
          v-for="admin in adminApps"
          :key="admin.name"
          class="flex h-[172px] items-center justify-center rounded-lg bg-white p-6 shadow"
        >
          <Button
            :to="admin.to"
            variant="link"
          >
            <img
              v-if="admin.logo"
              :src="admin.logo"
              :alt="admin.logo"
            />
          </Button>
        </div>
      </div>
    </div>
  </div>
  <TeleportSafe to="#page-footer">
    <div class="relative bg-[#353D59]">
      <img
        src="/cover/login.png"
        class="absolute right-21 bottom-0 w-1/2 md:w-1/5"
        alt="login"
      />
      <div class="mx-auto flex w-full max-w-7xl px-6 py-6">
        <p class="text-gray-400">
          © Copyright 2025 Finema
        </p>
      </div>
    </div>
  </TeleportSafe>
</template>

<script lang="ts" setup>
import { routes } from '~/constants/routes'

const apps = [
  {
    name: 'clockin',
    logo: '/login/clock-in-user.png',
    to: routes.clockin.home.to,
  },
  {
    name: 'timesheet',
    logo: '/login/time-sheet-user.png',
    to: routes.timesheet.home.to,
  },
  {
    name: 'pmo',
    logo: '/login/pmo-user.png',
    to: routes.pmo.home.to,
  },
]

const adminApps = [
  {
    name: 'clockin-admin',
    logo: '/login/clock-in-admin.png',
    to: routes.adminClockin.checkinDashboard.to,
  },
  {
    name: 'timesheet-admin',
    logo: '/login/time-sheet-admin.png',
    to: routes.adminTimesheet.summaryReport.to,
  },
  {
    name: 'super-admin',
    logo: '/login/pmo-admin.png',
    to: routes.adminPMO.home.to,
  },
]
</script>
