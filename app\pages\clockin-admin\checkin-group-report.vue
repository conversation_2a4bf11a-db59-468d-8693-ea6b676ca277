<template>
  <NuxtLayout>
    <CheckinGroupReport />
  </NuxtLayout>
</template>

<script lang="ts" setup>
import CheckinGroupReport from '~/features/clockinAdmin/CheckinGroupReport/index.vue'

definePageMeta({
  layout: 'clockin-admin',
})

useSeoMeta({
  title: routes.adminClockin.checkinGroupReport.label,
})

useApp().definePage({
  title: routes.adminClockin.checkinGroupReport.label,
  breadcrumbs: [routes.adminClockin.checkinGroupReport],
  sub_title: 'Checkin Group Report',
})
</script>
