import { defineEventHand<PERSON>, getQuery } from 'h3'

export default defineEventHandler((event) => {
  const query = getQuery(event)
  const {
    limit, page,
  } = query

  const items: any = [
    {
      id: 2845,
      created_at: '2025-07-02T03:01:45.707618+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'leave',
      timing: 8,
      detail: '',
      created_by: 'e6c379e3-5a65-4dcb-8eda-060392c23c1b',
      project_name: null,
      project_code: null,
      leave_type: 'Sick Leave',
      sga_name: null,
      profiles: {
        team: 'ACCOUNT',
        email: 'a<PERSON><EMAIL>',
        nickname: '<PERSON><PERSON>',
        avatar_url: null,
        display_name: '<PERSON><PERSON><PERSON>',
      },
    },
    {
      id: 3613,
      created_at: '2025-07-07T09:28:34.740577+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'sga',
      timing: 0.4,
      detail: 'กรอกข้อมูลรายงานฐานะการเงินของผู้ค้ำ(คุณศราวุธ) สำหรับขอเพิ่มวงเงินสินเชื่อ LG และ PN /ผู้ค้ำลงนาม/Scan File ชุดเอกสารส่งให้ RM KTB',
      created_by: '774614e7-2365-443b-802c-2e4446f3ecd6',
      project_name: null,
      project_code: '',
      leave_type: '',
      sga_name: 'FINEMA',
      profiles: {
        team: 'ACCOUNT',
        email: '<EMAIL>',
        nickname: 'Diow',
        avatar_url: null,
        display_name: '<EMAIL>',
      },
    },
    {
      id: 2838,
      created_at: '2025-07-01T23:27:35.048105+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'project',
      timing: 8,
      detail: 'Backend api',
      created_by: '9079de96-1905-4bb6-8814-8de1f51cea8e',
      project_name: 'MFA E passport',
      project_code: 'mfa-e-passport',
      leave_type: null,
      sga_name: null,
      profiles: {
        team: 'DEV',
        email: '<EMAIL>',
        nickname: null,
        avatar_url: 'https://avatars.slack-edge.com/2025-03-19/8628299114419_5bb3f17259a12c9a79e4_512.jpg',
        display_name: 'Nonchana Sangsom',
      },
    },
    {
      id: 4038,
      created_at: '2025-07-14T03:13:55.591187+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'internal',
      timing: 8,
      detail: 'support cloudculus',
      created_by: '928bcb34-d924-4949-9578-bbbe02a73914',
      project_name: null,
      project_code: null,
      leave_type: null,
      sga_name: null,
      profiles: {
        team: 'SUP',
        email: '<EMAIL>',
        nickname: null,
        avatar_url: 'https://avatars.slack-edge.com/2025-02-24/8496483191557_1d011496b6d83dd60c80_512.jpg',
        display_name: 'Supattra Ploydang',
      },
    },
    {
      id: 4965,
      created_at: '2025-07-22T04:30:13.674464+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'internal',
      timing: 8,
      detail: 'Design system 0.4  ',
      created_by: '800b1efd-86b5-4746-820a-b69c57a8f188',
      project_name: null,
      project_code: null,
      leave_type: null,
      sga_name: null,
      profiles: {
        team: 'UX',
        email: '<EMAIL>',
        nickname: null,
        avatar_url: 'https://avatars.slack-edge.com/2022-06-01/3605237082306_1c16507cd301c1f6f833_512.png',
        display_name: 'Tunchanok Boonchuay',
      },
    },
    {
      id: 3614,
      created_at: '2025-07-07T09:28:34.740577+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'sga',
      timing: 0.8,
      detail: 'เตรียมเอกสารสำหรับขออนุมัติสินเชื่อค่าธรรมเนียม lG ที่ใกล้ถึงกำหนดชำระ KTB ,ค่าเบี้ยประกันสินเชื่อ+ค่าผ่อนสินเชื่อ UOB ,ค่าผ่อนชำระสินเชื่อ SCB /แจ้งตั้งจ่ายโอนเงินให้ธนาคาร/สำเนาชุดเอกสาร Apporve ให้บัญชีลงรายการจ่าย<br />',
      created_by: '774614e7-2365-443b-802c-2e4446f3ecd6',
      project_name: null,
      project_code: '',
      leave_type: '',
      sga_name: 'FINEMA',
      profiles: {
        team: 'ACCOUNT',
        email: '<EMAIL>',
        nickname: 'Diow',
        avatar_url: null,
        display_name: '<EMAIL>',
      },
    },
    {
      id: 3615,
      created_at: '2025-07-07T09:28:34.740577+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'sga',
      timing: 0.4,
      detail: 'สรุปตาราง TR UOB  ,Factoring SME BANK ',
      created_by: '774614e7-2365-443b-802c-2e4446f3ecd6',
      project_name: null,
      project_code: '',
      leave_type: '',
      sga_name: 'FINEMA',
      profiles: {
        team: 'ACCOUNT',
        email: '<EMAIL>',
        nickname: 'Diow',
        avatar_url: null,
        display_name: '<EMAIL>',
      },
    },
    {
      id: 2790,
      created_at: '2025-07-01T06:11:50.07267+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'internal',
      timing: 2,
      detail: 'QTFT',
      created_by: '51111b8a-78f5-435e-9bad-1d5908eeb9b8',
      project_name: null,
      project_code: null,
      leave_type: null,
      sga_name: null,
      profiles: {
        team: 'PP',
        email: '<EMAIL>',
        nickname: null,
        avatar_url: 'https://avatars.slack-edge.com/2025-01-27/8354537542370_5cfc0136ab48c0f40890_512.png',
        display_name: 'Malairat Sawangrattanakun',
      },
    },
    {
      id: 2575,
      created_at: '2025-06-30T02:55:18.915469+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'internal',
      timing: 8,
      detail: 'Search and called Lead Accounting and Project Coordinator',
      created_by: '075ada26-79e1-401c-8058-da82350d4be4',
      project_name: null,
      project_code: null,
      leave_type: null,
      sga_name: null,
      profiles: {
        team: 'PP',
        email: '<EMAIL>',
        nickname: null,
        avatar_url: 'https://avatars.slack-edge.com/2025-06-16/9054506641844_ce77dcdcb25ead85a6fe_512.jpg',
        display_name: 'Natkrita Pojvijitr',
      },
    },
    {
      id: 3616,
      created_at: '2025-07-07T09:28:34.740577+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'internal',
      timing: 0.08,
      detail: 'แจ้งปัญหาอินเตอเน็ตขัดข้องของคอนโดพร้อมสุข /แคปรูป SMS แจ้งแม่พี่เอเจ อินเตอร์เน็ตใช้งานได้ปกติ',
      created_by: '774614e7-2365-443b-802c-2e4446f3ecd6',
      project_name: null,
      project_code: '',
      leave_type: '',
      sga_name: '',
      profiles: {
        team: 'ACCOUNT',
        email: '<EMAIL>',
        nickname: 'Diow',
        avatar_url: null,
        display_name: '<EMAIL>',
      },
    },
    {
      id: 2699,
      created_at: '2025-06-30T08:10:54.338636+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'sga',
      timing: 8,
      detail: 'ตรวจเอกสารเบิกเงินสดย่อย Project, กรอกใบแนบภงด.3/53',
      created_by: 'e03ba8c8-75ee-4851-b0ec-9e6b05fdcff4',
      project_name: null,
      project_code: null,
      leave_type: null,
      sga_name: 'FINEMA',
      profiles: {
        team: 'ACCOUNT',
        email: '<EMAIL>',
        nickname: 'Krit',
        avatar_url: 'https://avatars.slack-edge.com/2023-06-01/5352658109413_4cc63d1a950b64c41efc_512.jpg',
        display_name: 'Krittana',
      },
    },
    {
      id: 3508,
      created_at: '2025-07-07T07:13:09.781296+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'sga',
      timing: 8,
      detail: '',
      created_by: '89088ee8-1e64-499c-b633-75b3964e6328',
      project_name: null,
      project_code: '',
      leave_type: '',
      sga_name: 'FINEMA',
      profiles: {
        team: 'LEGAL',
        email: '<EMAIL>',
        nickname: 'Tee',
        avatar_url: null,
        display_name: '<EMAIL>',
      },
    },
    {
      id: 3253,
      created_at: '2025-07-04T04:04:07.126867+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'internal',
      timing: 4,
      detail: 'Follow up Peer Review 2024<br />Performance Appraisal 2025 <br />Outing Planning<br />Probation Review<br />Internal Support<br />Activity',
      created_by: 'fec397a7-7669-42af-b683-c23e20075171',
      project_name: null,
      project_code: null,
      leave_type: null,
      sga_name: null,
      profiles: {
        team: 'PP',
        email: '<EMAIL>',
        nickname: null,
        avatar_url: 'https://avatars.slack-edge.com/2025-04-10/8757374894256_133813ed83e1893d110d_512.png',
        display_name: 'Jiraporn Sadsaengchan',
      },
    },
    {
      id: 2788,
      created_at: '2025-07-01T06:11:38.762861+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'internal',
      timing: 4,
      detail: 'Payroll',
      created_by: '51111b8a-78f5-435e-9bad-1d5908eeb9b8',
      project_name: null,
      project_code: null,
      leave_type: null,
      sga_name: null,
      profiles: {
        team: 'PP',
        email: '<EMAIL>',
        nickname: null,
        avatar_url: 'https://avatars.slack-edge.com/2025-01-27/8354537542370_5cfc0136ab48c0f40890_512.png',
        display_name: 'Malairat Sawangrattanakun',
      },
    },
    {
      id: 3202,
      created_at: '2025-07-03T08:49:23.170613+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'project',
      timing: 0.8,
      detail: '',
      created_by: '309ce25d-d55b-4cfe-a11a-7cbda888ba54',
      project_name: 'DBD E-Registration สมาคมการค้า หอกาารค้า',
      project_code: 'dbd-eregistration',
      leave_type: '',
      sga_name: '',
      profiles: {
        team: 'ANL',
        email: '<EMAIL>',
        nickname: null,
        avatar_url: 'https://avatars.slack-edge.com/2023-09-15/5898997174309_8f8f8c26cdbddbd63ea4_512.png',
        display_name: 'Benjarat Chantaprasert',
      },
    },
    {
      id: 3201,
      created_at: '2025-07-03T08:49:23.170613+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'project',
      timing: 5.6,
      detail: '',
      created_by: '309ce25d-d55b-4cfe-a11a-7cbda888ba54',
      project_name: 'NT CMP',
      project_code: 'nt-cmp',
      leave_type: '',
      sga_name: '',
      profiles: {
        team: 'ANL',
        email: '<EMAIL>',
        nickname: null,
        avatar_url: 'https://avatars.slack-edge.com/2023-09-15/5898997174309_8f8f8c26cdbddbd63ea4_512.png',
        display_name: 'Benjarat Chantaprasert',
      },
    },
    {
      id: 3212,
      created_at: '2025-07-03T09:17:40.966555+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'leave',
      timing: 8,
      detail: '',
      created_by: '0f236500-57b7-4135-9972-81e39b463bd5',
      project_name: null,
      project_code: null,
      leave_type: 'Sick Leave',
      sga_name: null,
      profiles: {
        team: 'ANL',
        email: '<EMAIL>',
        nickname: null,
        avatar_url: 'https://avatars.slack-edge.com/2025-03-26/8659301451714_a3037d338e5f7604cdde_512.png',
        display_name: 'Benyapha Theanlow',
      },
    },
    {
      id: 3617,
      created_at: '2025-07-07T09:28:34.740577+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'internal',
      timing: 0.24,
      detail: 'สรุปตารางเปรียมเทียบราคาค่าเช่าคอนโดพร้อมสุข  (คุณหญิงแจ้งขอขึ้นค่าเช่าพื้นที่) ให้กรรมการพิจารณา สำหรับแจ้งทำสัญญาเช่าของปี 69',
      created_by: '774614e7-2365-443b-802c-2e4446f3ecd6',
      project_name: null,
      project_code: '',
      leave_type: '',
      sga_name: '',
      profiles: {
        team: 'ACCOUNT',
        email: '<EMAIL>',
        nickname: 'Diow',
        avatar_url: null,
        display_name: '<EMAIL>',
      },
    },
    {
      id: 2827,
      created_at: '2025-07-01T14:05:21.858855+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'project',
      timing: 3,
      detail: 'ทำ UI ฝั่ง admin',
      created_by: 'a80af83d-b09a-44a1-af45-ed27b4c39f46',
      project_name: 'NT CMP Phase 2',
      project_code: 'nt-cmp-phase-2',
      leave_type: null,
      sga_name: null,
      profiles: {
        team: 'UX',
        email: '<EMAIL>',
        nickname: null,
        avatar_url: 'https://avatars.slack-edge.com/2025-03-16/8608623788981_102768c05c9b76d84020_512.png',
        display_name: 'Nithita Hempaisanpipat',
      },
    },
    {
      id: 2816,
      created_at: '2025-07-01T10:18:06.637699+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'sga',
      timing: 3,
      detail: '1.ตรวจเงินเดือน<br />2.บันทึกเงินเดือน',
      created_by: '792f46f8-654f-47c9-8b0e-4cbf1e90698b',
      project_name: null,
      project_code: null,
      leave_type: null,
      sga_name: 'ENEX',
      profiles: {
        team: 'ACCOUNT',
        email: '<EMAIL>',
        nickname: 'Nat',
        avatar_url: null,
        display_name: '<EMAIL>',
      },
    },
    {
      id: 2814,
      created_at: '2025-07-01T10:11:30.353262+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'project',
      timing: 3,
      detail: 'ทำเอกสารส่งมอบ',
      created_by: 'b333f849-3755-444f-aea0-c9f34c9de2ce',
      project_name: 'DPT E-Platform',
      project_code: 'dpt-eplatform',
      leave_type: null,
      sga_name: null,
      profiles: {
        team: 'SOL',
        email: '<EMAIL>',
        nickname: null,
        avatar_url: 'https://avatars.slack-edge.com/2025-06-03/8992793562674_d0341b9209c57affb4d0_512.jpg',
        display_name: 'Jirayu Khotprathum',
      },
    },
    {
      id: 2880,
      created_at: '2025-07-02T07:40:27.404443+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'internal',
      timing: 5,
      detail: '',
      created_by: '3e398f4e-db73-4157-ac2a-3f9dd0e98291',
      project_name: null,
      project_code: null,
      leave_type: null,
      sga_name: null,
      profiles: {
        team: 'DEV',
        email: '<EMAIL>',
        nickname: null,
        avatar_url: 'https://avatars.slack-edge.com/2024-03-28/6872364349621_0fd29a6222783d5489d1_512.png',
        display_name: 'Kanittha Inbumrung',
      },
    },
    {
      id: 3204,
      created_at: '2025-07-03T08:49:23.170613+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'project',
      timing: 0.8,
      detail: '',
      created_by: '309ce25d-d55b-4cfe-a11a-7cbda888ba54',
      project_name: 'Aerothai Hybrid Cloud',
      project_code: 'aerothai-hybrid-cloud',
      leave_type: '',
      sga_name: '',
      profiles: {
        team: 'ANL',
        email: '<EMAIL>',
        nickname: null,
        avatar_url: 'https://avatars.slack-edge.com/2023-09-15/5898997174309_8f8f8c26cdbddbd63ea4_512.png',
        display_name: 'Benjarat Chantaprasert',
      },
    },
    {
      id: 3280,
      created_at: '2025-07-04T08:18:16.309911+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'project',
      timing: 2,
      detail: '',
      created_by: 'f3b0d51a-a6af-4f3e-937f-f9a7059b35d1',
      project_name: 'CAAT CMC Phase II',
      project_code: 'caat-cmc-phase-ii',
      leave_type: null,
      sga_name: null,
      profiles: {
        team: 'PM',
        email: '<EMAIL>',
        nickname: null,
        avatar_url: 'https://avatars.slack-edge.com/2024-11-01/7969117395027_3c40fd7c7c822f95cfaa_512.png',
        display_name: 'Natsuda Fuknil',
      },
    },
    {
      id: 3308,
      created_at: '2025-07-04T09:24:04.309085+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'internal',
      timing: 0.8,
      detail: 'Intern <br />1. Going thru the Verifined clients<br />2. Going thru her company deck and give suggestions <br />3. Discussion about EnAuthn events opportunities',
      created_by: '9ffe3336-2acd-4edf-9749-9e7201981d13',
      project_name: null,
      project_code: '',
      leave_type: '',
      sga_name: '',
      profiles: {
        team: 'BD',
        email: '<EMAIL>',
        nickname: null,
        avatar_url: 'https://avatars.slack-edge.com/2025-02-07/8415799882082_cbdf2e10bcfc826830c1_512.jpg',
        display_name: 'Wint Hmone Thant',
      },
    },
    {
      id: 2784,
      created_at: '2025-07-01T06:11:00.127115+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'internal',
      timing: 1,
      detail: 'PVD',
      created_by: '51111b8a-78f5-435e-9bad-1d5908eeb9b8',
      project_name: null,
      project_code: null,
      leave_type: null,
      sga_name: null,
      profiles: {
        team: 'PP',
        email: '<EMAIL>',
        nickname: null,
        avatar_url: 'https://avatars.slack-edge.com/2025-01-27/8354537542370_5cfc0136ab48c0f40890_512.png',
        display_name: 'Malairat Sawangrattanakun',
      },
    },
    {
      id: 4628,
      created_at: '2025-07-17T08:21:13.932815+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'leave',
      timing: 8,
      detail: '',
      created_by: '08456c08-7ef4-4a4f-afb0-dbffbc727631',
      project_name: null,
      project_code: null,
      leave_type: 'Birthday Leave',
      sga_name: null,
      profiles: {
        team: 'ADMIN',
        email: '<EMAIL>',
        nickname: 'Jelvyii',
        avatar_url: null,
        display_name: '<EMAIL>',
      },
    },
    {
      id: 3795,
      created_at: '2025-07-08T05:19:46.713072+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'project',
      timing: 8,
      detail: '',
      created_by: 'c242a889-f3cd-4751-9f6f-440d5965a14b',
      project_name: 'NT CMP',
      project_code: 'nt-cmp',
      leave_type: null,
      sga_name: null,
      profiles: {
        team: 'DEV',
        email: '<EMAIL>',
        nickname: null,
        avatar_url: 'https://avatars.slack-edge.com/2020-12-14/1568789061573_3ed947a36bb52deebe27_512.jpg',
        display_name: 'Jakkapong Promsana',
      },
    },
    {
      id: 3602,
      created_at: '2025-07-07T09:28:34.740577+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'sga',
      timing: 0.8,
      detail: 'ส่งเอกสารค่าใช้จ่ายให้บัญชีบันทึกรายการจ่าย/เสนอกรรมการขออนุมัติรายการที่ใกล้ถึงกำหนดชำระ/แนบ payslip แจ้งหลักฐานการชำระเงินส่งให้ Admin(คุณมิน) ',
      created_by: '774614e7-2365-443b-802c-2e4446f3ecd6',
      project_name: null,
      project_code: '',
      leave_type: '',
      sga_name: 'SPORT DAILY',
      profiles: {
        team: 'ACCOUNT',
        email: '<EMAIL>',
        nickname: 'Diow',
        avatar_url: null,
        display_name: '<EMAIL>',
      },
    },
    {
      id: 3603,
      created_at: '2025-07-07T09:28:34.740577+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'sga',
      timing: 0.24,
      detail: 'แจ้งส่ง E-mail ขอสรุปยอดรายการใช้จ่ายบัตรเครดิตบริษัทหลังวันตัดรอบบิล วันที่ 26/06/68 - 02/07/68 เพื่อนำยอดใช้จ่ายต่างประเทศไปยื่นภาษี /ติดตามการสมัครบริการ Visa Spend Clarity for Enterprise (คุณนพวุฒิ)',
      created_by: '774614e7-2365-443b-802c-2e4446f3ecd6',
      project_name: null,
      project_code: '',
      leave_type: '',
      sga_name: 'FINEMA',
      profiles: {
        team: 'ACCOUNT',
        email: '<EMAIL>',
        nickname: 'Diow',
        avatar_url: null,
        display_name: '<EMAIL>',
      },
    },
    {
      id: 3604,
      created_at: '2025-07-07T09:28:34.740577+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'sga',
      timing: 0.24,
      detail: 'แจ้งส่ง E-mail ขอสรุปยอดรายการใช้จ่ายบัตรเครดิตบริษัทหลังวันตัดรอบบิล วันที่ 26/06/68 - 02/07/68 เพื่อนำยอดใช้จ่ายต่างประเทศไปยื่นภาษี /ติดตามการสมัครบริการ Visa Spend Clarity for Enterprise (คุณนพวุฒิ)',
      created_by: '774614e7-2365-443b-802c-2e4446f3ecd6',
      project_name: null,
      project_code: '',
      leave_type: '',
      sga_name: 'CLOUDCULUS (THAILAND)',
      profiles: {
        team: 'ACCOUNT',
        email: '<EMAIL>',
        nickname: 'Diow',
        avatar_url: null,
        display_name: '<EMAIL>',
      },
    },
    {
      id: 2789,
      created_at: '2025-07-01T06:11:46.559513+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'external',
      timing: 2,
      detail: 'Agreement 360 ',
      created_by: '002cf8f4-d559-4469-a89e-310729bbdf78',
      project_name: null,
      project_code: null,
      leave_type: null,
      sga_name: null,
      profiles: {
        team: 'LEGAL',
        email: '<EMAIL>',
        nickname: 'Tungmay',
        avatar_url: null,
        display_name: '<EMAIL>',
      },
    },
    {
      id: 2786,
      created_at: '2025-07-01T06:11:23.89697+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'project',
      timing: 1.5,
      detail: 'Bizinside Meeting',
      created_by: '002cf8f4-d559-4469-a89e-310729bbdf78',
      project_name: 'BizInside',
      project_code: 'bizinside',
      leave_type: null,
      sga_name: null,
      profiles: {
        team: 'LEGAL',
        email: '<EMAIL>',
        nickname: 'Tungmay',
        avatar_url: null,
        display_name: '<EMAIL>',
      },
    },
    {
      id: 3605,
      created_at: '2025-07-07T09:28:34.740577+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'sga',
      timing: 0.8,
      detail: 'เตรียมเอกสารประกอบให้กรรมการลงนาม+ประทับตราสำหรับยื่นเรื่องให้ บสย. ค้ำประกันวงเงินสินเชื่อ ของ LH BANK /กรรมการลงนาม scan ให้เจ้าหน้าที่ธนาคาร /เรียก Grab นำส่งเอกสารให้เจ้าหน้าที่ธนาคาร',
      created_by: '774614e7-2365-443b-802c-2e4446f3ecd6',
      project_name: null,
      project_code: '',
      leave_type: '',
      sga_name: 'FINEMA',
      profiles: {
        team: 'ACCOUNT',
        email: '<EMAIL>',
        nickname: 'Diow',
        avatar_url: null,
        display_name: '<EMAIL>',
      },
    },
    {
      id: 2721,
      created_at: '2025-06-30T10:14:25.624538+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'project',
      timing: 3,
      detail: 'ตรวจสอบเอกสารยื่นเสนอราคา ยื่นเสนอราคา และติดตามผล พร้อมบันทึกเอกสาร',
      created_by: '6166f7ee-d2e1-4431-a4a5-1ca274a4ed2c',
      project_name: 'Aerothai Digital Signature',
      project_code: 'aerothai-digital-signature',
      leave_type: null,
      sga_name: null,
      profiles: {
        team: 'BC',
        email: '<EMAIL>',
        nickname: null,
        avatar_url: 'https://avatars.slack-edge.com/2024-07-21/7452034871077_06dc4df3c169a2ffbcec_512.png',
        display_name: 'Supanaree Mankong',
      },
    },
    {
      id: 3281,
      created_at: '2025-07-04T08:18:30.437167+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'internal',
      timing: 2,
      detail: '',
      created_by: 'f3b0d51a-a6af-4f3e-937f-f9a7059b35d1',
      project_name: null,
      project_code: null,
      leave_type: null,
      sga_name: null,
      profiles: {
        team: 'PM',
        email: '<EMAIL>',
        nickname: null,
        avatar_url: 'https://avatars.slack-edge.com/2024-11-01/7969117395027_3c40fd7c7c822f95cfaa_512.png',
        display_name: 'Natsuda Fuknil',
      },
    },
    {
      id: 2773,
      created_at: '2025-07-01T03:54:51.030511+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'project',
      timing: 1,
      detail: '',
      created_by: 'eb27cd7b-7f17-4c9f-b743-3554e77f1014',
      project_name: 'NCSA MA TI',
      project_code: 'ncsa-ma-ti',
      leave_type: null,
      sga_name: null,
      profiles: {
        team: 'ANL',
        email: '<EMAIL>',
        nickname: null,
        avatar_url: 'https://avatars.slack-edge.com/2024-05-06/7064878761046_287312d4344384f64d49_512.png',
        display_name: 'Pichchaporn Suriyanchaicharoen',
      },
    },
    {
      id: 2772,
      created_at: '2025-07-01T03:54:05.878252+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'project',
      timing: 3.5,
      detail: '',
      created_by: 'eb27cd7b-7f17-4c9f-b743-3554e77f1014',
      project_name: 'BizInside',
      project_code: 'bizinside',
      leave_type: null,
      sga_name: null,
      profiles: {
        team: 'ANL',
        email: '<EMAIL>',
        nickname: null,
        avatar_url: 'https://avatars.slack-edge.com/2024-05-06/7064878761046_287312d4344384f64d49_512.png',
        display_name: 'Pichchaporn Suriyanchaicharoen',
      },
    },
    {
      id: 2734,
      created_at: '2025-06-30T10:31:48.661913+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'project',
      timing: 1,
      detail: 'ประสาน ITK ส่งรายการสินค้าที่ต้องใช้ในโครงการ เพื่อขอใบเสนอราคา',
      created_by: '966ec8ba-193f-4ef3-ab31-7e6e5887f8f3',
      project_name: 'Excise Oil Control',
      project_code: 'excise-oil-control',
      leave_type: null,
      sga_name: null,
      profiles: {
        team: 'PS',
        email: '<EMAIL>',
        nickname: null,
        avatar_url: 'https://avatars.slack-edge.com/2025-03-18/8611897768742_5db79a1d3031b438cc8a_512.jpg',
        display_name: 'Sakaowrat Chuaychu',
      },
    },
    {
      id: 3618,
      created_at: '2025-07-07T09:28:34.740577+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'sga',
      timing: 0.24,
      detail: 'แจ้งฝ่ายกฎหมายดำเนินการขอเปลี่ยนแปลง บอจ.5 เป็นผู้ถือหุ้นก่อตั้งชุดเดิมเนื่องจาก SCB ขอคัดบอจ.5 ล่าสุด เพื่อประกอบการขออนุมัติปรับเงื่อนไขใหม่',
      created_by: '774614e7-2365-443b-802c-2e4446f3ecd6',
      project_name: null,
      project_code: '',
      leave_type: '',
      sga_name: 'JOINT VENTURE SKY EYE FUTURE',
      profiles: {
        team: 'ACCOUNT',
        email: '<EMAIL>',
        nickname: 'Diow',
        avatar_url: null,
        display_name: '<EMAIL>',
      },
    },
    {
      id: 2763,
      created_at: '2025-07-01T02:49:27.974551+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'leave',
      timing: 8,
      detail: 'ลาปวดท้องประจำเดือน ',
      created_by: 'a9b7ba94-c14c-4510-9aa9-707c8329f591',
      project_name: null,
      project_code: null,
      leave_type: 'Menstrual Leave',
      sga_name: null,
      profiles: {
        team: 'FN',
        email: '<EMAIL>',
        nickname: null,
        avatar_url: 'https://avatars.slack-edge.com/2025-05-27/8956098178434_1693080a6c56ce444508_512.png',
        display_name: 'Apiradee Toithong',
      },
    },
    {
      id: 3619,
      created_at: '2025-07-07T09:28:34.740577+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'sga',
      timing: 0.32,
      detail: 'ขอคัดหนังสือรับรองบริษัท+บอจ 5 กับ DBD',
      created_by: '774614e7-2365-443b-802c-2e4446f3ecd6',
      project_name: null,
      project_code: '',
      leave_type: '',
      sga_name: 'JOINT VENTURE SKY EYE FUTURE',
      profiles: {
        team: 'ACCOUNT',
        email: '<EMAIL>',
        nickname: 'Diow',
        avatar_url: null,
        display_name: '<EMAIL>',
      },
    },
    {
      id: 2750,
      created_at: '2025-06-30T10:41:36.391143+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'project',
      timing: 8,
      detail: '- นำเสนอผล VA&Pentest <br />- เตรียมเอกสาร เคลียร์เอกสารสำหรับส่งมอบงานงวดที่ 4<br />- ประสานงานเรื่องการนำเสนอราคาของโครงการใหม่<br />- ติดตามงานของโครงการที่จะต้องส่งมอบในงวดงานที่ 5 ',
      created_by: '127c4311-d2e1-476e-be88-1715d4a74f87',
      project_name: 'SME Data Warehouse II',
      project_code: 'sme-data-warehouse-ii',
      leave_type: null,
      sga_name: null,
      profiles: {
        team: 'PM',
        email: '<EMAIL>',
        nickname: null,
        avatar_url: 'https://avatars.slack-edge.com/2025-05-17/8896377089463_9b7cb74efcf48cd6f0c0_512.jpg',
        display_name: 'Supattra Yordthern',
      },
    },
    {
      id: 2764,
      created_at: '2025-07-01T02:58:05.038098+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'internal',
      timing: 8,
      detail: '1.สรุปรายงานผลการตรวจ Internal Audit Q1/68 จัดทำรายงานเพื่อใช้ในการประชุม<br />2.แก้ไขเอกสารISO27001:2022',
      created_by: 'da662542-7128-4765-a7c7-411e28157041',
      project_name: null,
      project_code: null,
      leave_type: null,
      sga_name: null,
      profiles: {
        team: 'IA',
        email: '<EMAIL>',
        nickname: null,
        avatar_url: 'https://avatars.slack-edge.com/2024-08-01/7533370155824_92686bb8fd01c98bbbf9_512.png',
        display_name: 'Phatchareephon Ripraphan',
      },
    },
    {
      id: 3307,
      created_at: '2025-07-04T09:24:04.309085+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'internal',
      timing: 2.4,
      detail: 'Thai Arrival Card<br />1. Google Ads Editing<br />2. Reposting reels and content<br />3. Going on reddit and writing comments<br />4. Applying arrival card <br />5. Guide P\'Nana for the application process<br />6. Editing terms of service and privacy policy on the website',
      created_by: '9ffe3336-2acd-4edf-9749-9e7201981d13',
      project_name: null,
      project_code: '',
      leave_type: '',
      sga_name: '',
      profiles: {
        team: 'BD',
        email: '<EMAIL>',
        nickname: null,
        avatar_url: 'https://avatars.slack-edge.com/2025-02-07/8415799882082_cbdf2e10bcfc826830c1_512.jpg',
        display_name: 'Wint Hmone Thant',
      },
    },
    {
      id: 2791,
      created_at: '2025-07-01T06:12:36.508028+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'internal',
      timing: 0.5,
      detail: 'Advisor Agreement Discuss ',
      created_by: '002cf8f4-d559-4469-a89e-310729bbdf78',
      project_name: null,
      project_code: null,
      leave_type: null,
      sga_name: null,
      profiles: {
        team: 'LEGAL',
        email: '<EMAIL>',
        nickname: 'Tungmay',
        avatar_url: null,
        display_name: '<EMAIL>',
      },
    },
    {
      id: 3203,
      created_at: '2025-07-03T08:49:23.170613+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'internal',
      timing: 0.8,
      detail: '',
      created_by: '309ce25d-d55b-4cfe-a11a-7cbda888ba54',
      project_name: null,
      project_code: '',
      leave_type: '',
      sga_name: '',
      profiles: {
        team: 'ANL',
        email: '<EMAIL>',
        nickname: null,
        avatar_url: 'https://avatars.slack-edge.com/2023-09-15/5898997174309_8f8f8c26cdbddbd63ea4_512.png',
        display_name: 'Benjarat Chantaprasert',
      },
    },
    {
      id: 2997,
      created_at: '2025-07-02T08:02:49.229667+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'project',
      timing: 8,
      detail: '',
      created_by: '6695038c-74a9-4627-a818-a9c0d62d1104',
      project_name: 'DBD E-Registration สมาคมการค้า หอกาารค้า',
      project_code: 'dbd-eregistration',
      leave_type: '',
      sga_name: '',
      profiles: {
        team: 'ANL',
        email: '<EMAIL>',
        nickname: null,
        avatar_url: 'https://avatars.slack-edge.com/2025-06-02/8987381900820_31583ee605620fae8ba0_512.jpg',
        display_name: 'Jakkrapat Santawaja',
      },
    },
    {
      id: 2725,
      created_at: '2025-06-30T10:15:16.072151+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'project',
      timing: 8,
      detail: '',
      created_by: '35bca82a-3d0f-42ec-ac9e-78682c9f1ee1',
      project_name: 'MFA E passport',
      project_code: 'mfa-e-passport',
      leave_type: null,
      sga_name: null,
      profiles: {
        team: 'BD',
        email: '<EMAIL>',
        nickname: null,
        avatar_url: 'https://avatars.slack-edge.com/2024-12-05/8132135811860_c3809b36c6bcbec3fb0c_512.png',
        display_name: 'Nithi Nitiyarom',
      },
    },
    {
      id: 2805,
      created_at: '2025-07-01T06:32:34.760379+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'external',
      timing: 8,
      detail: '',
      created_by: 'b1609bc8-16b7-461f-a6b8-76dff01e7aa4',
      project_name: null,
      project_code: '',
      leave_type: '',
      sga_name: '',
      profiles: {
        team: 'BD',
        email: '<EMAIL>',
        nickname: null,
        avatar_url: 'https://avatars.slack-edge.com/2025-03-14/8612207468257_234fbaa586a4c9fcd8d4_512.jpg',
        display_name: 'Chitsanupong Boonma',
      },
    },
    {
      id: 2811,
      created_at: '2025-07-01T10:02:49.336497+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'sga',
      timing: 3,
      detail: '1.ตั้งหนี้เงินสดย่อยโครงการ<br />2.ตรวจเอกสารชุดวางบิล',
      created_by: '792f46f8-654f-47c9-8b0e-4cbf1e90698b',
      project_name: null,
      project_code: null,
      leave_type: null,
      sga_name: 'FINEMA',
      profiles: {
        team: 'ACCOUNT',
        email: '<EMAIL>',
        nickname: 'Nat',
        avatar_url: null,
        display_name: '<EMAIL>',
      },
    },
    {
      id: 2813,
      created_at: '2025-07-01T10:10:46.477638+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'project',
      timing: 6,
      detail: 'ทำ PM พร้อมเอกสารส่งลูกค้า',
      created_by: 'b333f849-3755-444f-aea0-c9f34c9de2ce',
      project_name: 'NBTC Data Driven Organization MA',
      project_code: 'nbtc-data-driven-organization-ma',
      leave_type: null,
      sga_name: null,
      profiles: {
        team: 'SOL',
        email: '<EMAIL>',
        nickname: null,
        avatar_url: 'https://avatars.slack-edge.com/2025-06-03/8992793562674_d0341b9209c57affb4d0_512.jpg',
        display_name: 'Jirayu Khotprathum',
      },
    },
    {
      id: 2778,
      created_at: '2025-07-01T04:55:12.300842+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'project',
      timing: 4,
      detail: 'meeting with legal team / UI comment update',
      created_by: '8c1a819a-7d0e-4555-9398-34253f1a2bd7',
      project_name: 'BizInside',
      project_code: 'bizinside',
      leave_type: null,
      sga_name: null,
      profiles: {
        team: 'INNO',
        email: '<EMAIL>',
        nickname: null,
        avatar_url: 'https://avatars.slack-edge.com/2022-05-05/3478342074870_d30ea392b7c5f656598c_512.png',
        display_name: 'Wiphawee Maneengarm',
      },
    },
    {
      id: 2826,
      created_at: '2025-07-01T14:04:59.15571+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'internal',
      timing: 3,
      detail: 'ENEX Website<br />- แก้ไขดีไซน์ตามคอมเม้น',
      created_by: 'a80af83d-b09a-44a1-af45-ed27b4c39f46',
      project_name: null,
      project_code: null,
      leave_type: null,
      sga_name: null,
      profiles: {
        team: 'UX',
        email: '<EMAIL>',
        nickname: null,
        avatar_url: 'https://avatars.slack-edge.com/2025-03-16/8608623788981_102768c05c9b76d84020_512.png',
        display_name: 'Nithita Hempaisanpipat',
      },
    },
    {
      id: 2774,
      created_at: '2025-07-01T03:55:46.959261+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'internal',
      timing: 3.5,
      detail: '-Eat Drink & Be Happy via HR<br />- massage ',
      created_by: 'eb27cd7b-7f17-4c9f-b743-3554e77f1014',
      project_name: null,
      project_code: null,
      leave_type: null,
      sga_name: null,
      profiles: {
        team: 'ANL',
        email: '<EMAIL>',
        nickname: null,
        avatar_url: 'https://avatars.slack-edge.com/2024-05-06/7064878761046_287312d4344384f64d49_512.png',
        display_name: 'Pichchaporn Suriyanchaicharoen',
      },
    },
    {
      id: 3058,
      created_at: '2025-07-02T09:57:19.800659+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'project',
      timing: 5,
      detail: '',
      created_by: '0dee8179-9f3e-46b3-915d-cc364bc7eae2',
      project_name: 'DOE Data Lakehouse',
      project_code: 'doe-data-lakehouse',
      leave_type: null,
      sga_name: null,
      profiles: {
        team: 'PM',
        email: '<EMAIL>',
        nickname: null,
        avatar_url: 'https://avatars.slack-edge.com/2024-11-18/8066303795136_cdc0b712228dce190458_512.png',
        display_name: 'Wuttisak Saksit',
      },
    },
    {
      id: 3306,
      created_at: '2025-07-04T09:24:04.309085+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'internal',
      timing: 1.6,
      detail: 'PR<br />1. PR for Geneva Event<br />2. Going thru FNM-Namirial Joint Round session',
      created_by: '9ffe3336-2acd-4edf-9749-9e7201981d13',
      project_name: null,
      project_code: '',
      leave_type: '',
      sga_name: '',
      profiles: {
        team: 'BD',
        email: '<EMAIL>',
        nickname: null,
        avatar_url: 'https://avatars.slack-edge.com/2025-02-07/8415799882082_cbdf2e10bcfc826830c1_512.jpg',
        display_name: 'Wint Hmone Thant',
      },
    },
    {
      id: 2943,
      created_at: '2025-07-02T08:01:20.294467+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'project',
      timing: 8,
      detail: '',
      created_by: '375ee390-2aee-4de5-a194-016217de5ade',
      project_name: 'DBD E-Registration สมาคมการค้า หอกาารค้า',
      project_code: 'dbd-eregistration',
      leave_type: '',
      sga_name: '',
      profiles: {
        team: 'DEV',
        email: '<EMAIL>',
        nickname: null,
        avatar_url: 'https://avatars.slack-edge.com/2022-07-25/3845657848258_d62c53c1bb04f375b034_512.jpg',
        display_name: 'Suchart Sanluang',
      },
    },
    {
      id: 2747,
      created_at: '2025-06-30T10:37:42.216012+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'external',
      timing: 1,
      detail: 'SME - Data warehouse Phase 3<br />ปรับแก้ไขไฟล์สไลด์ พร้อมทั้งส่งอีเมล์อัพเดทลูกค้า',
      created_by: '966ec8ba-193f-4ef3-ab31-7e6e5887f8f3',
      project_name: null,
      project_code: null,
      leave_type: null,
      sga_name: null,
      profiles: {
        team: 'PS',
        email: '<EMAIL>',
        nickname: null,
        avatar_url: 'https://avatars.slack-edge.com/2025-03-18/8611897768742_5db79a1d3031b438cc8a_512.jpg',
        display_name: 'Sakaowrat Chuaychu',
      },
    },
    {
      id: 2745,
      created_at: '2025-06-30T10:36:39.249912+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'project',
      timing: 5,
      detail: 'ขึ้นโครงเอกสารโครงการ ส่วนของ ตาราง Comply ',
      created_by: '966ec8ba-193f-4ef3-ab31-7e6e5887f8f3',
      project_name: 'ONDE Learn 2 Earn',
      project_code: 'onde-learn-2-earn',
      leave_type: null,
      sga_name: null,
      profiles: {
        team: 'PS',
        email: '<EMAIL>',
        nickname: null,
        avatar_url: 'https://avatars.slack-edge.com/2025-03-18/8611897768742_5db79a1d3031b438cc8a_512.jpg',
        display_name: 'Sakaowrat Chuaychu',
      },
    },
    {
      id: 3305,
      created_at: '2025-07-04T09:24:04.309085+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'internal',
      timing: 3.2,
      detail: 'EnAuthn<br />1. Meeting with GLEIF<br />2. Meetings with potential clients and partners<br />3. NDAs signings<br />4. Follow ups<br />5. Going thru data swyft pilot proposal',
      created_by: '9ffe3336-2acd-4edf-9749-9e7201981d13',
      project_name: null,
      project_code: '',
      leave_type: '',
      sga_name: '',
      profiles: {
        team: 'BD',
        email: '<EMAIL>',
        nickname: null,
        avatar_url: 'https://avatars.slack-edge.com/2025-02-07/8415799882082_cbdf2e10bcfc826830c1_512.jpg',
        display_name: 'Wint Hmone Thant',
      },
    },
    {
      id: 2850,
      created_at: '2025-07-02T05:48:26.470898+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'leave',
      timing: 8,
      detail: '',
      created_by: 'd86793de-7ae7-4153-b9cd-4ae5c303b3a9',
      project_name: null,
      project_code: null,
      leave_type: 'Annual Leave',
      sga_name: null,
      profiles: {
        team: 'ACCOUNT',
        email: '<EMAIL>',
        nickname: 'PEA',
        avatar_url: null,
        display_name: '<EMAIL>',
      },
    },
    {
      id: 2651,
      created_at: '2025-06-30T03:53:40.465921+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'project',
      timing: 0.5,
      detail: '',
      created_by: '68ab1294-34b3-4d81-8a97-0dff60333ec6',
      project_name: 'CAAT SOMS',
      project_code: 'caat-replace-empic',
      leave_type: null,
      sga_name: null,
      profiles: {
        team: 'BD',
        email: '<EMAIL>',
        nickname: null,
        avatar_url: 'https://avatars.slack-edge.com/2023-12-19/6388599143088_e6a879b55cb4284211a2_512.png',
        display_name: 'Catherine Ssepuuya Nabbala',
      },
    },
    {
      id: 2726,
      created_at: '2025-06-30T10:15:17.63834+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'project',
      timing: 3,
      detail: 'เตรียมลิสเอกสารบุคลากร ตรวจสอบหาคน คุณสมบัติการยื่น',
      created_by: '6166f7ee-d2e1-4431-a4a5-1ca274a4ed2c',
      project_name: 'ONDE Learn 2 Earn',
      project_code: 'onde-learn-2-earn',
      leave_type: null,
      sga_name: null,
      profiles: {
        team: 'BC',
        email: '<EMAIL>',
        nickname: null,
        avatar_url: 'https://avatars.slack-edge.com/2024-07-21/7452034871077_06dc4df3c169a2ffbcec_512.png',
        display_name: 'Supanaree Mankong',
      },
    },
    {
      id: 3074,
      created_at: '2025-07-02T10:11:48.685435+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'project',
      timing: 1,
      detail: '',
      created_by: '0dee8179-9f3e-46b3-915d-cc364bc7eae2',
      project_name: 'NT Satellite 126E',
      project_code: 'nt-satellite-126e',
      leave_type: null,
      sga_name: null,
      profiles: {
        team: 'PM',
        email: '<EMAIL>',
        nickname: null,
        avatar_url: 'https://avatars.slack-edge.com/2024-11-18/8066303795136_cdc0b712228dce190458_512.png',
        display_name: 'Wuttisak Saksit',
      },
    },
    {
      id: 2933,
      created_at: '2025-07-02T07:58:33.527891+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'project',
      timing: 4,
      detail: '',
      created_by: 'b1455eb9-1f75-4331-bd8a-3a4c31ec6bce',
      project_name: 'NT CMP',
      project_code: 'nt-cmp',
      leave_type: '',
      sga_name: '',
      profiles: {
        team: 'DEV',
        email: '<EMAIL>',
        nickname: null,
        avatar_url: 'https://avatars.slack-edge.com/2025-04-01/8684215229173_7a2d8871f438626b37fe_512.jpg',
        display_name: 'Nanpipat Klinpratoom',
      },
    },
    {
      id: 2792,
      created_at: '2025-07-01T06:18:51.934018+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'internal',
      timing: 0.5,
      detail: 'ส่งเอกสาร Enkay',
      created_by: '002cf8f4-d559-4469-a89e-310729bbdf78',
      project_name: null,
      project_code: null,
      leave_type: null,
      sga_name: null,
      profiles: {
        team: 'LEGAL',
        email: '<EMAIL>',
        nickname: 'Tungmay',
        avatar_url: null,
        display_name: '<EMAIL>',
      },
    },
    {
      id: 2761,
      created_at: '2025-07-01T02:25:59.527731+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'sga',
      timing: 8,
      detail: 'บันทึกค่าใช้จ่าย TYN เดือน 1/67,ภาษีซื้อ เดือน 2/67',
      created_by: '0d5a529d-1d8f-4ae7-b6ca-377b5f1ce8ad',
      project_name: null,
      project_code: null,
      leave_type: null,
      sga_name: 'CLOUDCULUS (THAILAND)',
      profiles: {
        team: 'ACCOUNT',
        email: '<EMAIL>',
        nickname: 'Tukta',
        avatar_url: null,
        display_name: '<EMAIL>',
      },
    },
    {
      id: 2776,
      created_at: '2025-07-01T04:36:02.695428+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'project',
      timing: 8,
      detail: '',
      created_by: 'e8d2c403-39b9-465d-8165-7553ecf5bfd5',
      project_name: 'BizInside',
      project_code: 'bizinside',
      leave_type: null,
      sga_name: null,
      profiles: {
        team: 'INNO',
        email: '<EMAIL>',
        nickname: null,
        avatar_url: 'https://avatars.slack-edge.com/2025-07-07/9143307592951_c83388e39aca7b323610_512.png',
        display_name: 'Pratchaya Maneechot',
      },
    },
    {
      id: 2753,
      created_at: '2025-07-01T02:18:32.905691+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'internal',
      timing: 1.5,
      detail: 'ตรวจสอบความเรียบร้อยของคำฟ้อง (คำถูกผิด รูปแบบการจัดวาง) เช็คเอกสารท้ายคำฟ้องว่าตรงกับคำฟ้องหรือไม่ ',
      created_by: 'ee9d5984-c5e2-4a3c-a403-9c5456e60459',
      project_name: null,
      project_code: null,
      leave_type: null,
      sga_name: null,
      profiles: {
        team: 'LEGAL',
        email: '<EMAIL>',
        nickname: 'Baitoey',
        avatar_url: 'https://avatars.slack-edge.com/2025-06-09/9003743532183_682d5ca76c17baccdf10_512.jpg',
        display_name: 'Kanyarat Rattanabureewong',
      },
    },
    {
      id: 2879,
      created_at: '2025-07-02T07:40:10.137387+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'project',
      timing: 3,
      detail: '',
      created_by: '3e398f4e-db73-4157-ac2a-3f9dd0e98291',
      project_name: 'NT CMP Phase 2',
      project_code: 'nt-cmp-phase-2',
      leave_type: null,
      sga_name: null,
      profiles: {
        team: 'DEV',
        email: '<EMAIL>',
        nickname: null,
        avatar_url: 'https://avatars.slack-edge.com/2024-03-28/6872364349621_0fd29a6222783d5489d1_512.png',
        display_name: 'Kanittha Inbumrung',
      },
    },
    {
      id: 2815,
      created_at: '2025-07-01T10:16:53.169045+00:00',
      tracker_date: '2025-06-30',
      tracker_type: 'sga',
      timing: 2,
      detail: '1.ตรวจเอกสารชุดวางบิล<br />2.ตั้งหนี้',
      created_by: '792f46f8-654f-47c9-8b0e-4cbf1e90698b',
      project_name: null,
      project_code: null,
      leave_type: null,
      sga_name: 'QUANTUM TECHNOLOGY FOUNDATION (THAILAND)',
      profiles: {
        team: 'ACCOUNT',
        email: '<EMAIL>',
        nickname: 'Nat',
        avatar_url: null,
        display_name: '<EMAIL>',
      },
    },
  ].map((value) => ({
    value,
    sort: Math.random(),
  }))
    .sort((a, b) => a.sort - b.sort)
    .map(({
      value,
    }) => value)

  return {
    page: +(page || 1),
    total: 200,
    limit: +(limit || 30),
    count: items.length,
    items,
  }
})
