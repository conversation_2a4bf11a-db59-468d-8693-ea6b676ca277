<template>
  <div>
    <FormFields
      class="
        mb-2 grid w-full gap-4
        lg:grid-cols-6
      "
      :options="formFields"
    />
    <Table
      :options="tableOptions"
      @pageChange="user.fetchPageChange"
      @search="user.fetchSearch"
    >
      <template #user-cell="{ row }">
        <div class="flex items-center gap-2">
          <img
            v-if="row.original.profiles?.avatar_url"
            :src="row.original.profiles?.avatar_url "
            alt="icon"
            class="size-9 rounded-full"
          />
          <div class="truncate">
            <span class="font-bold">{{ row.original.profiles?.display_name }}</span>
            <div class="truncate text-[10px]">
              {{ TEAM_LABEL[row.original?.profiles?.team as TEAM] }}
            </div>
          </div>
        </div>
      </template>
      <template #team-cell="{ row }">
        <Badge
          variant="soft"
        >
          {{ TEAM_LABEL[row.original.team as TEAM] }}
        </Badge>
      </template>
      <template #status-cell="{ row }">
        <div class="flex justify-center">
          <Switch
            :model-value="row.original.status"
            :label="row.original.status ? 'Active' : 'Inactive'"
            disabled
          />
        </div>
      </template>
      <template #actions-cell="{ row }">
        <Button
          icon="mage:pen"
          variant="ghost"
          color="neutral"
          square
          :to="routes.admin.userById(row.original.id).to"
        />
      </template>
    </Table>
  </div>
</template>

<script lang="ts" setup>
import { COLUMN_TYPES } from '#core/components/Table/types'
import { useUserPageLoader } from '~/loaders/admin/user'
import { TEAM_LABEL, type TEAM } from '~/constants/team'

const user = useUserPageLoader()

const route = useRoute()
const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.SEARCH,
    class: 'lg:col-span-3',
    props: {
      name: 'q',
      placeholder: 'Search',
    },
  },
  {
    type: INPUT_TYPES.SELECT,
    props: {
      name: 'team',
      placeholder: 'All Team',
      options: [
        {
          label: 'Team A',
          value: 'team-a',
        },
        {
          label: 'Team B',
          value: 'team-b',
        },
      ],
    },
  },
  {
    type: INPUT_TYPES.SELECT,
    props: {
      name: 'status',
      placeholder: 'All Status',
      options: [
        {
          label: 'active',
          value: 'active',
        },
        {
          label: 'inactive',
          value: 'inactive',
        },
      ],
    },
  },
])

user.fetchSetLoading()
onMounted(() => {
  user.fetchPage(Number(route.query.page || 1), route.query.q as string, {
    params: route.query,
  })
})

const tableOptions = useTable({
  repo: user,
  columns: () => [
    {
      accessorKey: 'user',
      header: 'User',
      type: COLUMN_TYPES.TEXT,
    },
    {
      accessorKey: 'team',
      header: 'Team',
      type: COLUMN_TYPES.TEXT,
    },
    {
      accessorKey: 'created_at',
      header: 'Created At',
      type: COLUMN_TYPES.DATE,
    },

    {
      accessorKey: 'status',
      header: 'Status',
      meta: {
        class: {
          th: 'text-center',
        },
      },
    },
    {
      accessorKey: 'actions',
      header: '',
      meta: {
        class: {
          th: 'text-right',
          td: 'text-right',
        },
      },
    },
  ],
})
</script>
