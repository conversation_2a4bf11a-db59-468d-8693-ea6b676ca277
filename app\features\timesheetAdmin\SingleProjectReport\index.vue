<template>
  <Card class="w-full">
    <div class="relative">
      <h1 class="text-primary mb-4 text-3xl font-bold">
        Project Report
      </h1>
      <div class="mb-6 flex flex-col gap-4">
        <FormFields
          class="
            grid gap-4
            lg:grid-cols-2
          "
          :options="formFields"
        />
        <div class="flex h-8 justify-end">
          <Button
            size="sm"
            color="primary"
            @click="exportCSV"
          >
            Export Dashboard Data
          </Button>
        </div>
        <!-- <div
          v-if="dataProject.length > 0"
          class="mt-4 mb-2 text-xl font-bold"
        >
          สรุปโครงการ {{ form.values.project }} เดือน
          {{ TimeHelper.getDateFormTime(form.values.date || '') }}
          <br />
          <span class="text-lg"> รวมทั้งหมด: {{ totalProjectHour }} ชั่วโมง </span>
          <UTable
            :rows="dataProject"
            :columns="columns"
          />
          </div> -->
        <Table
          :options="tableOptions"
          @pageChange="trackers.fetchPageChange"
          @search="trackers.fetchSearch"
        >
          <template #type-team="{ row }">
            <Badge
              variant="soft"
            >
              {{ row.original.team }}
            </Badge>
          </template>

          <template #actions-cell="{ row }">
            <Button
              icon="iconamoon:eye-light"
              variant="ghost"
              color="neutral"
              square
              :to="row.original.link"
            />
          </template>
        </Table>
      </div>
    </div>
  </Card>
</template>

<script setup lang="ts">
import { useProjectsPageLoader } from '~/loaders/admin/project'
import { useSGAPageLoader } from '~/loaders/admin/sga'
import { useTrackersPageLoader } from '~/loaders/admin/tracker'

const pickedMonth = ref(getCurrentMonth())
const pickedYear = ref(getCurrentYear())
const selectedProject = ref('')
const groupedProjectTrackers = ref<Record<string, number>>({})
const projects = useProjectsPageLoader()
const sga = useSGAPageLoader()
const trackers = useTrackersPageLoader()

const form = useForm({
  validationSchema: toTypedSchema(v.object({
    date: v.optional(v.date(), new Date()),
    project: v.optional(v.string(), []),
  })),
  initialValues: {
    date: new Date(),
    project: '',
  },
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.SELECT,
    props: {
      name: 'project',
      placeholder: 'เลือกโปรเจกต์',
      label: 'Project',
      options: projectOptions.value || [],
    },
  },
  {
    type: INPUT_TYPES.DATE,
    props: {
      name: 'date',
      label: 'Date',
      updateMonthYear: (month: number, year: number) => {
        pickedMonth.value = month + 1
        pickedYear.value = year
      },
      monthPicker: true,
    },
  },
])

const tableOptions = useTable({
  repo: trackers,
  columns: () => [
    {
      accessorKey: 'user',
      header: 'User Name',
      type: COLUMN_TYPES.TEXT,
    },
    {
      accessorKey: 'hour',
      header: 'Hour',
      type: COLUMN_TYPES.DATE,
    },
    {
      accessorKey: 'percent',
      header: 'Percent (%)',
    },
  ],
})

const groupTracker = () => {
  const grouped: Record<string, number> = {}

  trackers.fetch.items?.forEach((item) => {
    const name = item.profiles?.display_name || 'Unknown'

    grouped[name] = (grouped[name] || 0) + item.timing
  })

  groupedProjectTrackers.value = grouped
}

projects.fetchSetLoading()
trackers.fetchSetLoading()
sga.fetchSetLoading()
onMounted(() => {
  projects.fetchPage()
  sga.fetchPage()
  trackers.fetchPage()
  groupTracker()
})

const columns = computed(() => [
  {
    key: 'display_name',
    label: 'Project Name',
  },
  {
    key: 'timing',
    label: 'Hour',
  },
  {
    key: 'percent',
    label: 'Percent (%)',
  },
])

const dataProject = computed(() => {
  const projectTrackers = groupedProjectTrackers.value
  const total = Object.values(projectTrackers).reduce((sum, val) => sum + val, 0)

  return Object.entries(projectTrackers).map(([name, timing]) => ({
    display_name: name,
    timing: timing.toFixed(2),
    percent: total > 0 ? ((timing / total) * 100).toFixed(2) : '0.00',
  }))
})

const totalProjectHour = computed(() => {
  return Object.values(groupedProjectTrackers.value)
    .reduce((sum, val) => sum + val, 0)
    .toFixed(2)
})

const projectOptions = computed(() => {
  const normalProjects = (projects.fetch.items || []).map((p) => ({
    label: p.name,
    value: p.id,
  }))

  const sgaProjects = (sga.fetch.items || []).map((p) => ({
    label: p.name + ' (SGA)',
    value: p.id,
  }))

  return [
    {
      label: 'External',
      value: 'external',
    },
    {
      label: 'Internal',
      value: 'internal',
    },
    ...normalProjects,
    ...sgaProjects,
  ]
})

const exportCSV = () => {
  let csv = ''
  let filename = ''

  const rows = dataProject.value

  csv += 'User,Hour,Percent (%)\n'

  for (const row of rows) {
    csv += `"${row.display_name}",${row.timing},${row.percent}\n`
  }

  filename = `project_${selectedProject.value.label}_${pickedMonth.value}_${pickedYear.value}.csv`

  const blob = new Blob(['\uFEFF' + csv], {
    type: 'text/csv;charset=utf-8;',
  })

  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')

  link.href = url
  link.setAttribute('download', filename)
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}
</script>

<style scoped>
::v-deep(.custom-datepicker .dp__input) {
  height: 32px !important;
}
</style>
