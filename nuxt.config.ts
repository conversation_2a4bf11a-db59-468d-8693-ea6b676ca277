// https://nuxt.com/docs/api/configuration/nuxt-config
import { SITE } from './app/constants/site'

export default defineNuxtConfig({
  modules: ['@nuxt/eslint', '@nuxt/test-utils', '@finema/core'],
  imports: {
    dirs: ['constants', 'loaders', 'helpers', 'types'],
  },
  devtools: {
    enabled: true,
  },
  app: {
    head: {
      title: SITE.TITLE,
    },
  },
  css: ['~/assets/css/main.css'],
  ui: {
    theme: {
      colors: [
        'white',
      ],
    },
  },
  compatibilityDate: '2025-07-15',
  vite: {
    optimizeDeps: {
      include: [
        'chart.js',
        'vue-chartjs',
        '@vue/devtools-kit',
        '@vue/devtools-core',
      ],
    },
  },
  eslint: {
    config: {
      stylistic: true,
      tooling: true,
    },
  },
})
