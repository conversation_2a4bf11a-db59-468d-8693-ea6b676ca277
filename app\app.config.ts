import { SITE } from './constants/site'

export default defineAppConfig({
  core: {
    site_name: SITE.TITLE,
    is_thai_year: true,
    is_thai_month: true,
    date_format: 'dd MMM yyyy',
    date_time_format: 'dd/MM/yyyy HH:mm น.',
    color: '#335AFF',
    limit_per_page: 15,
  },
  ui: {
    colors: {
      white: 'white',
    },
    dialog: {
      slots: {
        overlay: 'bg-black/50 backdrop-blur',
      },
    },
    slideover: {
      slots: {
        overlay: 'bg-black/50 backdrop-blur',
      },
    },
    card: {
      slots: {
        root: '!bg-white',
        header: 'text-lg font-bold',
      },
      variants: {
        variant: {
          soft: {
            root: 'bg-elevated/50 divide-y divide-default shadow-[0px_2px_14px_0px_rgba(0,0,0,0.06)]',
          },
        },
      },
    },
    switch: {
      slots: {
        base: 'cursor-pointer',
        label: 'cursor-pointer',
      },
    },
    breadcrumb: {
      variants: {
        active: {
          true: {
            link: 'text-black font-semibold',
          },
        },
      },
    },
    tabs: {
      slots: {
        trigger: 'data-[state=active]:font-bold cursor-pointer',
      },
      defaultVariants: {
        size: 'xl',
      },
    },
    modal: {
      slots: {
        overlay: 'bg-black/50 backdrop-blur',
        title: 'text-xl font-bold',
        content: 'divide-none',
        body: 'sm:pt-0 pt-0',
        footer: 'w-full',
      },
    },
    pagination: {
      slots: {
        first: 'disabled:hidden',
        prev: 'disabled:hidden',
        next: 'disabled:hidden',
        last: 'disabled:hidden',
      },
    },
    table: {
      slots: {
        root: 'rounded-t-md rounded-b-md bg-white',
        captionContainer: 'hidden',
        paginationInfo: 'text-gray-600',
        paginationContainer: 'items-center flex-col lg:flex-row gap-4',
        thead: 'bg-[#475569]',
        th: 'text-white whitespace-nowrap',
        td: 'text-[#222222]',
      },
    },
    formField: {
      slots: {
        label: 'font-bold',
      },
    },
    input: {
      variants: {
        size: {
          xl: {
            base: 'py-2.5 disabled:bg-[#F5F5F5] disabled:text-[#737373] disabled:cursor-not-allowed',
          },
        },
      },
      defaultVariants: {
        size: 'xl',
      },
    },
    dateTime: {
      slots: {
        clearIcon: 'size-6 mr-3',
      },
    },
    selectMenu: {
      slots: {
        base: 'cursor-pointer w-full',
        item: 'cursor-pointer',
        clearIcon: 'size-6',
      },
      variants: {
        size: {
          xl: {
            base: 'py-2.5 disabled:bg-[#F5F5F5] disabled:text-[#737373] disabled:cursor-not-allowed',
          },
        },
      },
      defaultVariants: {
        size: 'xl',
      },
    },
    textarea: {
      variants: {
        size: {
          xl: {
            base: 'py-2.5 disabled:bg-[#F5F5F5] disabled:text-[#737373] disabled:cursor-not-allowed',
          },
        },
      },
      defaultVariants: {
        size: 'xl',
      },
    },
    button: {
      slots: {
        base: ['rounded-lg cursor-pointer'],
      },
      variants: {
        size: {
          xl: {
            base: 'py-2.5 font-semibold',
          },
        },
      },
      compoundVariants: [
        {
          color: 'white',
          variant: 'solid',
          class: 'text-black',
        },
      ],
      defaultVariants: {
        size: 'xl',
      },
    },
    stepper: {
      variants: {
        size: {
          xs: {
            trigger: 'size-8',
            icon: 'size-6',
            title: 'text-base font-bold',
            description: 'text-sm',
            wrapper: 'mt-1.5',
          },
        },
      },
      defaultVariants: {
        size: 'xs',
      },
    },
  },
})
