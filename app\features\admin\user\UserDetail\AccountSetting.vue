<template>
  <div class="flex justify-between">
    <div class="text-sm">
      <div class="font-bold">
        เปลี่ยนแปลงสถานะการใช้งานบัญชี
      </div>
      การปิดสถานะการใช้งาน เป็นการยกเลิกการใช้งานบัญชีชั่วคราว
      <br />ผู้ใช้งานจะไม่สามารถเข้าใช้งานระบบได้ แต่ข้อมูลและสถิติต่าง ๆ จะยังถูกคำนวณอยู่
    </div>
    <Switch
      :model-value="isActive"
      :label="isActive ? 'Active' : 'Inactive'"
      @update:modelValue="() => onSwitch('')"
    />
  </div>
  <Separator class="mt-7 mb-5" />
  <div class="flex justify-between">
    <div class="text-sm">
      <div class="mb-4 font-bold">
        ลบบัญชีผู้ใช้งานถาวร
      </div>
      การลบข้อมูลจะไม่สามารถเรียกคืนบัญชีและข้อมูลของผู้ใช้งานกลับมาได้อีก
    </div>
    <Button
      icon="octicon:circle-slash"
      class="h-10"
      color="error"
      label="ลบบัญชีผู้ใช้งาน"
    />
  </div>
  <Separator class="mt-7 mb-5" />
</template>

<script lang="ts" setup>
const isActive = ref(true)

const onSwitch = (value: string) => {
  isActive.value = !isActive.value
}
</script>
