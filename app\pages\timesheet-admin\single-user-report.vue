<template>
  <NuxtLayout>
    <SingleUserReport />
  </NuxtLayout>
</template>

<script lang="ts" setup>
import SingleUserReport from '~/features/timesheetAdmin/SingleUserReport/index.vue'

definePageMeta({
  layout: 'timesheet-admin',
})

useSeoMeta({
  title: routes.adminTimesheet.singleUserReport.label,
})

useApp().definePage({
  title: routes.adminTimesheet.singleUserReport.label,
  breadcrumbs: [routes.adminTimesheet.singleUserReport],
  sub_title: 'Single User Report',
})
</script>
