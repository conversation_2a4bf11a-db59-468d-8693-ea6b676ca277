<template>
  <NuxtLayout>
    <UserDetail />
  </NuxtLayout>
</template>

<script lang="ts" setup>
import UserDetail from '~/features/admin/user/UserDetail/index.vue'

definePageMeta({
  layout: 'admin',
})

const route = useRoute()
const id = route.params.id as string

useSeoMeta({
  title: routes.admin.userById(id).label,
})

useApp().definePage({
  breadcrumbs: [routes.admin.users, routes.admin.userById(id)],
})
</script>
