<template>
  <div class="mb-6 flex flex-col gap-4">
    <form>
      <div
        class="
            grid gap-x-10 gap-y-5
            lg:grid-cols-2
          "
      >
        <FormFields :options="formFields" />
      </div>
      <div class="mt-4">
        <Button
          type="submit"
          size="xl"
          class="w-fit"
        >
          Search
        </Button>
      </div>
    </form>
    <div class="grid">
      <Table
        :options="tableOptions"
        @pageChange="trackers.fetchPage"
        @search="trackers.fetchSearch"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import * as v from 'valibot'
import { INPUT_TYPES } from '#core/components/Form/types'
import { TEAM_OPTIONS } from '~/constants/team'
import { useHolidaysPageLoader } from '~/loaders/admin/holiday'
import { useTrackersPageLoader } from '~/loaders/admin/tracker'

const companyHolidays = useHolidaysPageLoader()
const trackers = useTrackersPageLoader()
const form = useForm({
  validationSchema: toTypedSchema(v.object({
    date: v.optional(v.date(), new Date()),
    teams: v.optional(v.array(v.string()), []),
  })),
  initialValues: {
    date: new Date(),
    teams: [],
  },
})

const teamDisplayLabel = computed(() => {
  const currentTeams = form.values.teams || []

  if (currentTeams.length === 0) {
    return 'All Teams'
  }

  return currentTeams
    .map((value: string) => TEAM_OPTIONS.find((option) => option.value === value)?.label)
    .filter(Boolean)
    .join(', ')
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.DATE,
    class: 'lg:col-span-1',
    props: {
      name: 'date',
      label: 'Date',
      placeholder: 'Select Date',
      monthPicker: true,
      maxDate: new Date(),
      autoApply: true,
    },
  },
  {
    type: INPUT_TYPES.SELECT_MULTIPLE,
    class: 'lg:col-span-1',
    props: {
      name: 'teams',
      label: 'Team',
      placeholder: teamDisplayLabel.value,
      options: TEAM_OPTIONS,
      clearable: true,
    },
  },
])

companyHolidays.fetchSetLoading()
trackers.fetchSetLoading()
onMounted(() => {
  companyHolidays.fetchPage()
  trackers.fetchPage()
})

const tableOptions = useTable<any>({
  repo: trackers,
  options: {
    isHidePagination: false,
  },
  columns: () => [
    {
      accessorKey: 'date',
      header: 'Date',
      type: COLUMN_TYPES.TEXT,
    },
    {
      accessorKey: 'checkin_type',
      header: 'Check-in type',
      type: COLUMN_TYPES.TEXT,
    },
    {
      accessorKey: 'check-in',
      header: 'Check-in',
      type: COLUMN_TYPES.TEXT,
    },
  ],
})
</script>
