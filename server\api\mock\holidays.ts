import { defineEvent<PERSON>and<PERSON>, getQuery } from 'h3'

export default defineEventHandler((event) => {
  const query = getQuery(event)
  const {
    limit, page,
  } = query

  const items: any = [
    {
      id: 6,
      created_at: '2025-05-28T06:49:11.616632+00:00',
      name: 'Labour Day',
      holiday_date: '2025-05-01',
    },
    {
      id: 7,
      created_at: '2025-05-28T06:49:44.313276+00:00',
      name: 'Visakha Bucha Day (Sub)',
      holiday_date: '2025-05-12',
    },
    {
      id: 8,
      created_at: '2025-05-28T06:49:59.177617+00:00',
      name: '<PERSON><PERSON><PERSON>',
      holiday_date: '2025-07-10',
    },
    {
      id: 9,
      created_at: '2025-05-28T06:50:27.13867+00:00',
      name: '<PERSON><PERSON><PERSON>. King Rama X BD',
      holiday_date: '2025-07-28',
    },
    {
      id: 10,
      created_at: '2025-05-28T06:52:46.435204+00:00',
      name: 'Queen\'s Mother BD',
      holiday_date: '2025-08-12',
    },
    {
      id: 11,
      created_at: '2025-05-28T06:53:10.374696+00:00',
      name: 'Chulalongkorn Day',
      holiday_date: '2025-10-23',
    },
    {
      id: 12,
      created_at: '2025-05-28T06:53:27.054302+00:00',
      name: 'Father\'s Day',
      holiday_date: '2025-12-05',
    },
    {
      id: 13,
      created_at: '2025-05-28T06:53:42.050331+00:00',
      name: 'Constitutional Day',
      holiday_date: '2025-12-10',
    },
    {
      id: 14,
      created_at: '2025-05-28T06:54:01.018734+00:00',
      name: 'New Year\'s Eve',
      holiday_date: '2025-12-31',
    },
    {
      id: 15,
      created_at: '2025-06-09T06:13:58.2448+00:00',
      name: 'ชดเชยวันจักรี',
      holiday_date: '2025-04-07',
    },
    {
      id: 16,
      created_at: '2025-06-09T06:14:27.560308+00:00',
      name: 'สงกรานต์',
      holiday_date: '2025-04-14',
    },
    {
      id: 17,
      created_at: '2025-06-09T06:14:42.622044+00:00',
      name: 'สงกรานต์',
      holiday_date: '2025-04-15',
    },
    {
      id: 18,
      created_at: '2025-06-09T06:14:54.778175+00:00',
      name: 'สงกรานต์',
      holiday_date: '2025-04-16',
    },
    {
      id: 19,
      created_at: '2025-06-09T06:15:45.534563+00:00',
      name: 'วันแรงงาน',
      holiday_date: '2025-05-01',
    },
    {
      id: 20,
      created_at: '2025-06-09T06:16:09.705928+00:00',
      name: 'ชดเชยวันวิสาขบูชา',
      holiday_date: '2025-05-12',
    },
  ].map((value) => ({
    value,
    sort: Math.random(),
  }))
    .sort((a, b) => a.sort - b.sort)
    .map(({
      value,
    }) => value)

  return {
    page: +(page || 1),
    total: 200,
    limit: +(limit || 30),
    count: items.length,
    items,
  }
})
