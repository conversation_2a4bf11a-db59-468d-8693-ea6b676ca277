import type { NavigationMenuItem } from '@nuxt/ui'

export const routes = {
  home: {
    label: 'หน้าแรก',
    to: '/',
  },
  chooseTeam: {
    label: 'เลือกทีม',
    to: '/choose-team',
  },
  login: {
    label: 'เลือก',
    to: '/login',
  },
  logout: {
    label: 'ออกจากระบบ',
    to: '/api/auth/logout',
  },
  account: {
    profile: {
      label: 'โปรไฟล์',
      to: '/account/profile',
      icon: 'mage:user',
    },
  },

  announcements: {
    label: 'Finema Newsletter',
    to: '/announcements',
    icon: 'mage:announcement',
  },

  adminClockin: {
    checkinDashboard: {
      label: 'ภาพรวมเช็คอิน',
      icon: 'mage:dashboard',
      to: '/clockin-admin/checkin-dashboard',
    },
    checkinGroupReport: {
      label: 'ภาพรวมตามกลุ่ม',
      icon: 'heroicons-outline:user-group',
      to: '/clockin-admin/checkin-group-report',
    },
    checkinIndividualReport: {
      label: 'ภาพรวมตามบุคคล',
      icon: 'mage:user',
      to: '/clockin-admin/checkin-individual-report',
    },
  },
  adminTimesheet: {
    summaryReport: {
      label: 'ภาพรวม',
      icon: 'heroicons-outline:user-group',
      to: '/timesheet-admin/summary-report',
    },
    timesheetReport: {
      label: 'รายงาน',
      icon: 'heroicons-outline:table-cells',
      to: '/timesheet-admin/timesheet-report',
    },
    singleUserReport: {
      label: 'รายงานผู้ใช้งาน',
      icon: 'heroicons-outline:document-chart-bar',
      to: '/timesheet-admin/single-user-report',
    },
    singleProjectReport: {
      label: 'รายงานโครงการ',
      icon: 'heroicons-outline:chart-bar',
      to: '/timesheet-admin/single-project-report',
    },
  },

  adminPMO: {
    home: {
      label: 'ภาพรวม',
      icon: 'mage:dashboard',
      to: '/pmo-admin',
    },
  },
  clockin: {
    home: {
      label: 'ภาพรวม',
      icon: 'mage:dashboard',
      to: '/clockin',
    },
  },
  timesheet: {
    home: {
      label: 'ภาพรวม',
      icon: 'mage:dashboard',
      to: '/timesheet',
    },
  },
  pmo: {
    home: {
      label: 'ภาพรวม',
      icon: 'mage:dashboard',
      to: '/pmo',
    },
  },
  admin: {
    users: {
      label: 'จัดการผู้ใช้งาน',
      icon: 'hugeicons:user-circle-02',
      to: '/admin/users',
    },
    userById: (id: string, label = '') => ({
      label: label || 'รายละเอียดผู้ใช้งาน',
      to: `/admin/users/${id}`,
    }),
    holidays: {
      label: 'จัดการวันหยุดบริษัท',
      icon: 'bi:calendar-date',
      to: '/admin/holidays',
    },
    projects: {
      label: 'จัดการกระทรวง/กรม',
      icon: 'proicons:bank',
      to: '/admin/projects',
    },
    sgas: {
      label: 'จัดการ SGAs',
      icon: 'hugeicons:layout-04',
      to: '/admin/sgas',
    },
    teams: {
      label: 'จัดการทีม',
      icon: 'lucide:users',
      to: '/admin/teams',
    },
  },

} as const

export const sidebarAdmin: NavigationMenuItem[] = [
  routes.admin.users,
  routes.admin.holidays,
  routes.admin.projects,
  routes.admin.sgas,
  routes.admin.teams,
]

export const sidebarUser: NavigationMenuItem[] = [

]

export const sidebarAdminClockin: NavigationMenuItem[] = [
  routes.adminClockin.checkinDashboard,
  routes.adminClockin.checkinGroupReport,
  routes.adminClockin.checkinIndividualReport,
]

export const sidebarAdminTimesheet: NavigationMenuItem[] = [
  routes.adminTimesheet.summaryReport,
  routes.adminTimesheet.timesheetReport,
  routes.adminTimesheet.singleUserReport,
  routes.adminTimesheet.singleProjectReport,
]
