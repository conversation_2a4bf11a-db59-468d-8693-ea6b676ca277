<template>
  <div class="relative mb-4 flex items-start justify-between">
    <Button
      label="Export"
      icon="proicons:add-square-multiple"
      size="xl"
      variant="outline"
      class="mb-2"
      @click="exportToCSV"
    />
  </div>
  <div class="mb-6 flex flex-col gap-4">
    <Form>
      <FormFields
        :options="formField"
      />
    </Form>
    <Button
      label="Search"
      size="xl"
      class="w-fit"
      @click="fetchCheckins"
    />
    <div
      v-if="selectedMemberDisplayName && paginatedCheckin.length"
      class="rounded-xl border"
    >
      <div class="flex items-center gap-2 border-b p-6 text-lg font-semibold">
        {{ selectedMemberDisplayName }}
        <Badge
          :label="teamOptions.find((team) => team.value === form.values.team)?.label ?? ''"
          variant="subtle"
        />
      </div>
      <div class="grid">
        <Table
          :options="tableOptions"
          @pageChange="checkinLoader.fetchPage"
          @search="checkinLoader.fetchSearch"
        >
          <template #checkin_type-cell="{ row }">
            <div class="flex flex-col items-start gap-1">
              <div
                v-for="(item, index) in row.original.original_items"
                :key="index"
                class="flex items-center gap-1"
              >
                <img
                  :src="useGetIconCheckin(item)"
                  alt="icon"
                  class="size-6"
                />
                {{ getLabel(item) }}
                <div v-if="item.checkin_type?.endsWith('Leave')">
                  ({{ item.leave_period?.startsWith('HALF') ? 'Half Day' : 'Full Day' }})
                </div>
                <div v-else-if="item.checkin_type === CHECKIN.ONSITE">
                  ({{ item.location }})
                </div>
              </div>
            </div>
          </template>
          <template #check-in-cell="{ row }">
            <Badge
              v-bind="getCheckinBadge(getCheckinStatus(row.original))"
              variant="subtle"
            />
          </template>
        </Table>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { CheckinItem } from '~/types'
import { CHECKIN } from '~/constants/checkin_types'
import { GROUP_LABELS } from '~/constants/icons'
import { TEAM_OPTIONS } from '~/constants/team'
import { useUserPageLoader } from '~/loaders/admin/user'
import { useCheckinsPageLoader } from '~/loaders/admin/checkin'

const noti = useNotification()
const page = ref(1)
const pageSize = 10
const pickedMonth = ref(getCurrentMonth())
const pickedYear = ref(getCurrentYear())
const teamOptions = [...TEAM_OPTIONS]

const checkinLoader = useCheckinsPageLoader()
const profileLoader = useUserPageLoader()

checkinLoader.fetchSetLoading()
onMounted(() => {
  fetchCheckins()
  fetchMembers()
})

const form = useForm({
  validationSchema: toTypedSchema(
    v.object({
      date: v.optional(v.pipe(v.string(), v.nonEmpty('')), ''),
      team: v.optional(v.pipe(v.string(), v.nonEmpty('')), ''),
      member: v.optional(v.pipe(v.string(), v.nonEmpty('')), ''),
    }),
  ),
})

const formField = createFormFields(() => [
  {
    type: INPUT_TYPES.DATE,
    props: {
      name: 'date',
      label: 'Date',
      placeholder: 'Select Date',
      enableTimePicker: false,
      autoApply: true,
      range: true,
      maxDate: new Date(),
    },
  },
  {
    type: INPUT_TYPES.SELECT,
    props: {
      name: 'team',
      label: 'Team',
      placeholder: 'team',
      options: TEAM_OPTIONS,
      searchable: true,
    },
  },
  {
    type: INPUT_TYPES.SELECT,
    props: {
      name: 'member',
      label: 'Member',
      placeholder: 'member',
      loading: !profileLoader.fetch.status.isLoaded,
      options: profileLoader.fetch.items?.map((item: any) => ({
        label: item.display_name,
        value: item.id,
      })) || [],
      searchable: true,
    },
  },
])

const tableOptions = useTable<any>({
  repo: checkinLoader,
  options: {
    isHidePagination: false,
  },
  columns: () => [
    {
      accessorKey: 'date',
      header: 'Date',
      type: COLUMN_TYPES.TEXT,
    },
    {
      accessorKey: 'checkin_type',
      header: 'Check-in type',
      type: COLUMN_TYPES.TEXT,
    },
    {
      accessorKey: 'check-in',
      header: 'Check-in',
      type: COLUMN_TYPES.TEXT,
    },
  ],
})

const selectedMemberDisplayName = computed(() => {
  return profileLoader.fetch.items?.find((m: any) => m.id === form.values.member)?.display_name ?? ''
})

const groupedCheckins = computed(() => {
  const checkinData = checkinLoader.fetch.items || []
  const groups: Record<string, CheckinItem[]> = {}
  const unusedFlags: Record<string, boolean> = {}

  for (const item of checkinData) {
    const key = item.date

    if (item.is_unused === true) {
      unusedFlags[key] = true
      continue
    }

    if (!groups[key]) groups[key] = []
    groups[key].push(item as CheckinItem)
  }

  return Object.entries(groups).map(([date, items]) => {
    return {
      date,
      checkin_types: items.map((i) => i.checkin_type),
      original_items: items,
      display_name: items?.at(0)?.profiles?.display_name,
      location: items?.at(0)?.location,
      original_timestamp: items?.at(0)?.original_timestamp,
      is_unused: unusedFlags[date],
    }
  })
})

const paginatedCheckin = computed(() => {
  const start = (page.value - 1) * pageSize
  const end = start + pageSize

  return groupedCheckins.value.slice(start, end)
})

const getLabel = (data: CheckinItem): string => {
  if (data.checkin_type?.startsWith('OFFICE')) {
    return GROUP_LABELS[data.checkin_type] || ''
  } else if (data.checkin_type?.endsWith('Leave')) {
    const leaveType = data.checkin_type.replace('Leave', '').toLowerCase()

    return GROUP_LABELS[leaveType] ?? data.checkin_type
  }

  return GROUP_LABELS[data.checkin_type] ?? data.checkin_type
}

const getCheckinStatus = (row: any) => {
  const dateStr = TimeHelper.getDateFormTime(row.date)
  const original = TimeHelper.getDateFormTime(row.original_timestamp)
  const isEdited = row.is_unused === true

  if (dateStr >= original) {
    if (isEdited) return 'On-time (Edited)'

    return 'On-time'
  }

  if (isEdited) return 'Late (Edited)'

  return 'Late'
}

const getCheckinBadge = (status: string) => {
  switch (status) {
    case 'On-time':
      return {
        color: 'success' as const,
        label: 'On-time',
      }
    case 'Late':
      return {
        color: 'warning' as const,
        label: 'Late',
      }
    case 'On-time (Edited)':
      return {
        color: 'success' as const,
        label: 'On-time (Edited)',
      }
    case 'Late (Edited)':
      return {
        color: 'warning' as const,
        label: 'Late (Edited)',
      }
    default:
      return {
        color: 'neutral' as const,
        label: 'Unknown',
      }
  }
}

const fetchCheckins = async () => {
  page.value = 1
  const params = {
    select: 'date,created_at,user_id,checkin_type,original_timestamp,leave_period,location,is_unused,remark,profiles(display_name,team,email)',
    user_id: `eq.${form.values.member}`,
    order: 'date.asc',
  }

  checkinLoader.fetchPage(1, '', {
    params: {
      ...params,
    },
  })
}

const fetchMembers = async () => {
  const params = {
    select: '*',
    team: `eq.${form.values.team}`,
  }

  await profileLoader.fetchPage(1, '', {
    params: {
      ...params,
    },
  })
}

const exportToCSV = () => {
  if (!groupedCheckins.value.length) {
    noti.warning({
      icon: 'mdi-close-circle',
      title: `No Data`,
    })

    return
  }

  const rows = groupedCheckins.value.map((item) => {
    const types = item.original_items.map((i) => getLabel(i)).join(' / ')
    const periodDetails = item.original_items
      .map((i) => {
        if (i.checkin_type?.endsWith('Leave')) {
          return i.leave_period?.startsWith('HALF') ? 'Half Day' : 'Full Day'
        } else if (i.checkin_type === CHECKIN.ONSITE) {
          return i.location ?? ''
        }

        return ''
      })
      .join(' / ')

    return {
      'Date': item.date,
      'Name': item.display_name,
      'Check-in Types': types,
      'Detail': periodDetails,
      'Status': getCheckinStatus(item),
    }
  })

  const headers = Object.keys(rows[0] ?? {}) as (keyof typeof rows[0])[]
  const csvContent = [
    headers.join(','),
    ...rows.map((row) =>
      headers.map((h) => `"${(row[h] ?? '').toString().replace(/"/g, '""')}"`).join(','),
    ),
  ].join('\n')

  const blob = new Blob([csvContent], {
    type: 'text/csv;charset=utf-8;',
  })

  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')

  link.setAttribute('href', url)
  link.setAttribute(
    'download',
    `Individual_Report_${selectedMemberDisplayName.value || 'export'}.csv`,
  )

  link.click()
}
</script>

<style scoped>
::v-deep(.custom-datepicker .dp__input) {
  height: 32px !important;
}
</style>
