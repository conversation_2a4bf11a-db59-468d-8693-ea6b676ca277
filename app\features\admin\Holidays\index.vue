<template>
  <div>
    <FormFields
      class="
        mb-2 grid w-full gap-4
        lg:grid-cols-6
      "
      :options="formFields"
    />
    <Table
      :options="tableOptions"
      @pageChange="holidays.fetchPageChange"
      @search="holidays.fetchSearch"
    >
      <template #type-team="{ row }">
        <Badge
          variant="soft"
        >
          {{ row.original.team }}
        </Badge>
      </template>

      <template #actions-cell="{ row }">
        <DropdownMenu :items="items(row)">
          <Button
            variant="ghost"
            icon="flowbite:dots-vertical-solid"
          />
        </DropdownMenu>
      </template>
    </Table>
  </div>
</template>

<script lang="ts" setup>
import { COLUMN_TYPES } from '#core/components/Table/types'
import { useHolidaysPageLoader } from '~/loaders/admin/holiday'

const holidays = useHolidaysPageLoader()

const route = useRoute()
const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.SEARCH,
    class: 'lg:col-span-3',
    props: {
      name: 'q',
      placeholder: 'Search',
    },
  },

])

const items = (row: any) => [
  [
    {
      label: 'Edit',
      icon: 'i-heroicons-pencil-square-20-solid',
      click: async () => {
        console.log('Edit', row.original.id)
      },
    },
  ],
  [
    {
      label: 'Delete',
      icon: 'i-heroicons-trash-20-solid',
      click: async () => {
        console.log('Delete', row.original.id)
      },
    },
  ],
]

const tableOptions = useTable({
  options: {
    isRouteChange: true,
  },
  repo: holidays,
  columns: () => [
    {
      accessorKey: 'name',
      header: 'Name',
      type: COLUMN_TYPES.TEXT,
      meta: {
        class: {
          th: 'w-2/3',
        },
      },
    },
    {
      accessorKey: 'holiday_date',
      header: 'Date',
      type: COLUMN_TYPES.DATE,
    },
    {
      accessorKey: 'actions',
      header: '',
      meta: {
        class: {
          th: 'text-right',
          td: 'text-right',
        },
      },
    },
  ],
})

holidays.fetchSetLoading()
onMounted(() => {
  holidays.fetchPage(Number(route.query.page || 1), route.query.q as string, {
    params: route.query,
  })
})
</script>
