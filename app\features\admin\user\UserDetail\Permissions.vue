<template>
  <Form @submit="onSubmit">
    <div class="rounded-xl border border-[#EAECF0]">
      <div class="flex items-center bg-gray-50 px-6 py-3 text-sm font-medium">
        <div class="w-1/2">
          System
        </div>
        <div class="w-1/2">
          Access Level
        </div>
      </div>
      <div
        v-for="system in systems"
        :key="system.name"
        class="flex items-center border-t border-[#EAECF0] px-6 py-4 first:border-t-0"
      >
        <div class="flex w-1/2 items-center gap-3">
          <img
            :src="system.icon"
            class="size-10 rounded-full"
            alt="profile_mock"
          />
          <span class="text-sm font-medium">{{ system.name }}</span>
        </div>
        <div class="w-1/2">
          <FormFields
            :options="system.fieldForm"
          />
        </div>
      </div>
    </div>
  </Form>
  <Separator class="mt-4 mb-5" />
  <div class="flex justify-end gap-4">
    <Button
      label="Save Changes"
      type="submit"
    />
  </div>
</template>

<script lang="ts" setup>
const noti = useNotification()
const form = useForm({
  validationSchema: toTypedSchema(v.object({
    clockIn: v.optional(v.pipe(v.string()), ''),
    timeSheet: v.optional(v.pipe(v.string()), ''),
    PMO: v.optional(v.pipe(v.string()), ''),
    superAdminSetting: v.optional(v.pipe(v.string()), ''),
  })),
  initialValues: {
    clockIn: 'user',
    timeSheet: 'user',
    PMO: 'no-access',
    superAdminSetting: 'no-access',
  },
})

const systems = ref([
  {
    icon: '/admin/clock-in.png',
    name: 'Clock-in',
    fieldForm: createFormFields(() => [
      {
        type: INPUT_TYPES.SELECT,
        props: {

          name: 'clockIn',
          options: [
            {
              label: 'User',
              value: 'user',
            },
            {
              label: 'Admin',
              value: 'admin',
            },
          ],
        },
      },
    ]),
  },
  {
    icon: '/admin/timesheet.png',
    name: 'Timesheet',
    fieldForm: createFormFields(() => [
      {
        type: INPUT_TYPES.SELECT,
        props: {

          name: 'timeSheet',
          options: [
            {
              label: 'User',
              value: 'user',
            },
            {
              label: 'Admin',
              value: 'admin',
            },
          ],
        },
      },
    ]),
  },
  {
    icon: '/admin/pmo.png',
    name: 'PMO',
    fieldForm: createFormFields(() => [
      {
        type: INPUT_TYPES.SELECT,
        props: {

          name: 'PMO',
          options: [
            {
              label: 'No Access',
              value: 'no-access',
            },
            {
              label: 'Admin',
              value: 'admin',
            },
          ],
        },
      },
    ]),
  },
  {
    icon: '/admin/super-admin.png',
    name: 'Super Admin Setting',
    fieldForm: createFormFields(() => [
      {
        type: INPUT_TYPES.SELECT,
        props: {

          name: 'superAdminSetting',
          options: [
            {
              label: 'No Access',
              value: 'no-access',
            },
            {
              label: 'Admin',
              value: 'admin',
            },
          ],
        },
      },
    ]),
  },
])

const onSubmit = form.handleSubmit((values) => {
  noti.success({
    title: 'Save Changes',
    description: 'Item updated successfully.',
  })
})
</script>
