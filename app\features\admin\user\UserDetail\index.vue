<template>
  <div class="mb-8 flex justify-between">
    <div class="flex gap-6">
      <img
        src="/profile_mock.png"
        class="size-[86px] rounded-full"
        alt="profile_mock"
      />
      <div>
        <div class="text-3xl font-semibold">
          Kwang
        </div>
        <div>
          Kanittha Inbumrung
        </div>
        <Badge
          color="warning"
          label="Manager Team"
          variant="soft"
        />
      </div>
    </div>
    <div>
      <Button
        variant="outline"
        color="neutral"
      >
        <div class="bg-success size-2 rounded-full" />Active
      </Button>
    </div>
  </div>
  <Card class="h-[589px]">
    <div class="flex">
      <div class="max-w-[300px] flex-1 space-y-1 border-r border-r-[#EAECF0] pr-4">
        <div
          v-for="item in menuItems"
          :key="item.key"
          class="grid w-full cursor-pointer rounded-lg px-3 py-[10px]"
          :class="selected === item.key ? 'bg-[#F9FAFB]' : ''"
          @click="selected = item.key"
        >
          <div class="font-bold">
            {{ item.title }}
          </div>
          <div class="text-xs text-[#475467]">
            {{ item.subtitle }}
          </div>
        </div>
      </div>
      <div
        class="flex-1 md:ml-[30px]"
      >
        <div class="text-xl font-bold">
          {{ menuItems.find(item => item.key === selected)?.title }}
        </div>
        <div class="mb-10 text-[#475467]">
          {{ menuItems.find(item => item.key === selected)?.subtitle }}
        </div>
        <UserInfo v-if="selected === 'userInfo'" />
        <AccountSetting v-else-if="selected === 'accountSetting'" />
        <Permissions v-else-if="selected === 'permissions'" />
      </div>
    </div>
  </Card>
</template>

<script lang="ts" setup>
import AccountSetting from './AccountSetting.vue'
import UserInfo from './UserInfo.vue'
import Permissions from './Permissions.vue'

const selected = ref<string>('permissions')

const menuItems = [
  {
    key: 'userInfo',
    title: 'ข้อมูลผู้ใช้งาน',
    subtitle: 'User Info.',
  },
  {
    key: 'permissions',
    title: 'สิทธิ์การใช้งาน',
    subtitle: 'Permissions',
  },
  {
    key: 'accountSetting',
    title: 'จัดการบัญชีผู้ใช้',
    subtitle: 'Account Setting',
  },
]
</script>
