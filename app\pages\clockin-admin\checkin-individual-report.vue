<template>
  <NuxtLayout>
    <IndividualReport />
  </NuxtLayout>
</template>

<script lang="ts" setup>
import IndividualReport from '~/features/clockinAdmin/IndividualReport/index.vue'

definePageMeta({
  layout: 'clockin-admin',
})

useSeoMeta({
  title: routes.adminClockin.checkinIndividualReport.label,
})

useApp().definePage({
  title: routes.adminClockin.checkinIndividualReport.label,
  breadcrumbs: [routes.adminClockin.checkinIndividualReport],
  sub_title: 'Checkin Individual Report',
})
</script>
