<template>
  <div class="flex min-h-screen flex-col">
    <div class="min-h-full flex-1">
      <div class="relative mx-auto mt-[246px] max-w-md">
        <img
          src="/Isolation_mode.png"
          class="absolute top-[-246px] right-1/2 w-[310px] translate-x-1/2"
          alt="login"
        />
        <div class="bg-main-500 relative grid h-[50px] place-items-center rounded-t-xl">
          <img
            src="/logo-mini-white.png"
            class=""
            alt="logo-mini"
          />
        </div>
        <div class="relative rounded-b-xl bg-white p-6">
          <Form @submit="onSubmit">
            <FormFields
              :options="fieldsCloudServices"
            />
            <Separator class="my-5" />
            <div class="flex justify-end">
              <Button
                label="ตกลง"
                type="submit"
              />
            </div>
          </Form>
        </div>
      </div>
    </div>
  </div>

  <div class="relative bg-[#353D59]">
    <img
      src="/cover/login.png"
      class="absolute -right-5 bottom-0 w-1/2 md:w-2/5"
      alt="login"
    />
    <div class="mx-auto flex w-full max-w-7xl px-6 py-6">
      <p class="text-gray-400">
        © Copyright 2025 Finema
      </p>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { TEAM_OPTIONS } from '~/constants/team'
import { routes } from '~/constants/routes'

const router = useRouter()
const form = useForm({
  validationSchema: toTypedSchema(v.object({
    name: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณาระบุชื่อเล่นภาษาอังกฤษ')), ''),
    team: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณาเลือกทีม')), ''),
  })),
})

const fieldsCloudServices = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'ชื่อเล่นภาษาอังกฤษ (Display Name)',
      name: 'name',
      required: true,
    },
  },
  {
    type: INPUT_TYPES.SELECT,
    props: {
      label: 'ทีม (Team)',
      name: 'team',
      required: true,
      options: TEAM_OPTIONS,
    },
  },
])

const onSubmit = form.handleSubmit((values) => {
  console.log(values)
  router.push(routes.home.to)
})
</script>
