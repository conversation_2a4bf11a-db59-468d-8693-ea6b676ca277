<template>
  <div class="mb-6 flex flex-col gap-4">
    <Form>
      <FormFields
        :options="formField"
      />
    </Form>
    <Button
      label="Search"
      size="xl"
      class="w-fit"
    />

    <div class="grid">
      <Table
        :options="tableOptions"
        @pageChange="checkinLoader.fetchPage"
        @search="checkinLoader.fetchSearch"
      >
        <template #checkin_type-cell="{ row }">
          <div class="flex flex-col items-start gap-1">
            <div
              v-for="(item, index) in row.original.original_items"
              :key="index"
              class="flex items-center gap-1"
            >
              <img
                :src="useGetIconCheckin(item)"
                alt="icon"
                class="size-6"
              />
              {{ getLabel(item) }}
              <div v-if="item.checkin_type?.endsWith('Leave')">
                ({{ item.leave_period?.startsWith('HALF') ? 'Half Day' : 'Full Day' }})
              </div>
              <div v-else-if="item.checkin_type === CHECKIN.ONSITE">
                ({{ item.location }})
              </div>
            </div>
          </div>
        </template>
        <template #check-in-cell="{ row }">
          <Badge
            v-bind="getCheckinBadge(getCheckinStatus(row.original))"
            variant="subtle"
          />
        </template>
      </Table>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { CHECKIN } from '~/constants/checkin_types'
import { TEAM_OPTIONS } from '~/constants/team'
import { useUserPageLoader } from '~/loaders/admin/user'
import { useCheckinsPageLoader } from '~/loaders/admin/checkin'

const checkinLoader = useCheckinsPageLoader()
const profileLoader = useUserPageLoader()

checkinLoader.fetchSetLoading()
onMounted(() => {
  checkinLoader.fetchPage()
})

const form = useForm({
  validationSchema: toTypedSchema(
    v.object({
      date: v.optional(v.pipe(v.string(), v.nonEmpty('')), ''),
      team: v.optional(v.pipe(v.string(), v.nonEmpty('')), ''),
      member: v.optional(v.pipe(v.string(), v.nonEmpty('')), ''),
    }),
  ),
})

const formField = createFormFields(() => [
  {
    type: INPUT_TYPES.DATE,
    props: {
      name: 'date',
      label: 'Date',
      placeholder: 'Select Date',
      enableTimePicker: false,
      autoApply: true,
      range: true,
      maxDate: new Date(),
    },
  },
  {
    type: INPUT_TYPES.SELECT,
    props: {
      name: 'team',
      label: 'Team',
      placeholder: 'team',
      options: TEAM_OPTIONS,
      searchable: true,
    },
  },
])

const tableOptions = useTable<any>({
  repo: checkinLoader,
  options: {
    isHidePagination: false,
  },
  columns: () => [
    {
      accessorKey: 'date',
      header: 'Date',
      type: COLUMN_TYPES.TEXT,
    },
    {
      accessorKey: 'checkin_type',
      header: 'Check-in type',
      type: COLUMN_TYPES.TEXT,
    },
    {
      accessorKey: 'check-in',
      header: 'Check-in',
      type: COLUMN_TYPES.TEXT,
    },
  ],
})
</script>

<style scoped>
::v-deep(.custom-datepicker .dp__input) {
  height: 32px !important;
}
</style>
