<template>
  <NuxtLayout>
    <CheckinDashboard />
  </NuxtLayout>
</template>

<script lang="ts" setup>
import CheckinDashboard from '~/features/clockinAdmin/CheckinDashboard/index.vue'

definePageMeta({
  layout: 'clockin-admin',
})

useSeoMeta({
  title: routes.adminClockin.checkinDashboard.label,
})

useApp().definePage({
  title: routes.adminClockin.checkinDashboard.label,
  breadcrumbs: [routes.adminClockin.checkinDashboard],
  sub_title: 'Checkin Dashboard',
})
</script>
