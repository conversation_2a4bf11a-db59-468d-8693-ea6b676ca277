import { defineEventHand<PERSON>, getQuery } from 'h3'

export default defineEventHandler((event) => {
  const query = getQuery(event)
  const {
    limit, page,
  } = query

  const items: any = [
    {
      id: 30,
      created_at: '2025-06-05T04:58:54.849689+00:00',
      name: 'ENEX',
    },
    {
      id: 14,
      created_at: '2025-05-28T09:23:20.250962+00:00',
      name: 'ENKAY STUDIO',
    },
    {
      id: 15,
      created_at: '2025-05-28T09:23:20.250962+00:00',
      name: 'FINEMA',
    },
    {
      id: 16,
      created_at: '2025-05-28T09:23:20.250962+00:00',
      name: 'FINEMA LAB',
    },
    {
      id: 17,
      created_at: '2025-05-28T09:23:20.250962+00:00',
      name: 'FINEMA SOLUTIONS (THAILAND)',
    },
    {
      id: 18,
      created_at: '2025-05-28T09:23:20.250962+00:00',
      name: 'FINEMA TECHNOLOGIES',
    },
    {
      id: 19,
      created_at: '2025-05-28T09:23:20.250962+00:00',
      name: 'FINERGY',
    },
    {
      id: 20,
      created_at: '2025-05-28T09:23:20.250962+00:00',
      name: 'GOVERNMENT TRANSFORMATION',
    },
    {
      id: 23,
      created_at: '2025-05-28T09:23:20.250962+00:00',
      name: 'JOINT VENTURE SKY EYE FUTURE',
    },
    {
      id: 24,
      created_at: '2025-05-28T09:23:20.250962+00:00',
      name: 'KERD KOR SAB',
    },
    {
      id: 26,
      created_at: '2025-05-28T09:23:20.250962+00:00',
      name: 'NERD & CO',
    },
    {
      id: 27,
      created_at: '2025-05-28T09:23:20.250962+00:00',
      name: 'QUANTUM TECHNOLOGY FOUNDATION (THAILAND)',
    },
    {
      id: 28,
      created_at: '2025-05-28T09:23:20.250962+00:00',
      name: 'SKY EYE TECH',
    },
    {
      id: 13,
      created_at: '2025-05-28T09:23:20.250962+00:00',
      name: 'CLOUDCULUS (THAILAND)',
    },
    {
      id: 12,
      created_at: '2025-05-28T09:23:20.250962+00:00',
      name: 'SPORT DAILY',
    },
  ].map((value) => ({
    value,
    sort: Math.random(),
  }))
    .sort((a, b) => a.sort - b.sort)
    .map(({
      value,
    }) => value)

  return {
    page: +(page || 1),
    total: 200,
    limit: +(limit || 30),
    count: items.length,
    items,
  }
})
