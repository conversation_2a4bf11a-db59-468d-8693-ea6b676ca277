export const useAuth = () => {
  const {
    auth,
  } = useRequestOptions()

  const token = useCookie('token', {
    path: '/',
    maxAge: 60 * 60 * 24 * 365,
  })

  const isAuthenticated = computed(() => !!token.value)
  const me = defineStore('auth.me', () => {
    const value = ref<IUser | null>(null)

    const set = (user: IUser | null) => {
      value.value = user
    }

    return {
      value,
      set,
    }
  })()

  const fetchMe = (() => {
    return useObjectLoader<IUser>({
      method: 'GET',
      url: '/me',
      getRequestOptions: auth,
    })
  })()

  const login = async () => {
    const config = useRuntimeConfig()

    window.location.href = config.public.baseAPI + '/auth/slack-login'
  }

  useWatchTrue(() => fetchMe.status.value.isSuccess, () => {
    me.set(fetchMe.data.value)
  })

  useWatchTrue(() => fetchMe.status.value.isError, () => {
    token.value = undefined
    me.set(null)
  })

  return {
    token,
    login,
    isAuthenticated,
    me,
    fetchMe,
  }
}
