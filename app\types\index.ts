export interface ITracker {
  id: number
  created_at: string
  tracker_date: string
  tracker_type: string
  project_name?: string
  project_code?: string
  leave_type?: string
  sga_name?: string
  timing: number
  detail: string
  created_by: string
  profiles: Profile
}

export interface TrackerDetails {
  projectName?: string
  timing: number
  date?: string
  profileName?: string
  sgaName?: string
  leaveName?: string
}

export interface GroupedPersonalTrackers {
  project: { totalHours: number
    details: TrackerDetails[] }
  leave: { totalHours: number
    details: TrackerDetails[] }
  sga: { totalHours: number
    details: TrackerDetails[] }
  ot: { totalHours: number
    details: TrackerDetails[] }
  internal: { totalHours: number
    details: TrackerDetails[] }
  external: { totalHours: number
    details: TrackerDetails[] }
  standby: { totalHours: number
    details: TrackerDetails[] }
}

export interface CheckinItem {
  user_id: string
  checkin_type: string
  leave_period?: string
  location?: string
  is_unused?: boolean
  created_at: string
  remark?: string
  original_timestamp: string
  date: string
  profiles: Profile
}

export interface GroupedCheckin {
  date: string
  original_timestamp: string
  detail: Array<Omit<CheckinItem, 'date'>>
}

export interface Profile {
  display_name: string
  email: string
  id: string
  team: string
  avatar_url?: string
  nickname?: string
}

export interface Holidays {
  holiday_date: string
  name: string
  id: string
}

export interface Project {
  id: string
  name: string
  code: string
  created_at: string
}

export interface SgaTypes {
  id: string
  name: string
  created_at: string
}
