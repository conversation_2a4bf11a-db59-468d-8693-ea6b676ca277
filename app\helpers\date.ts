export const getCurrentYear = (): number => {
  const dateStr = TimeHelper.getCurrentDateTime() ?? ''

  return Number.parseInt(dateStr.split('-')[0] ?? '0', 10)
}

export const getCurrentMonth = (): number => {
  const dateStr = TimeHelper.getCurrentDateTime() ?? ''

  return Number.parseInt(dateStr.split('-')[1] ?? '0', 10)
}

export const getCurrentDate = (): number => {
  const dateStr = TimeHelper.getCurrentDateTime() ?? ''

  return Number.parseInt(dateStr.split('-')[2] ?? '0', 10)
}
