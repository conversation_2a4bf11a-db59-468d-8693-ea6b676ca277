<template>
  <NuxtLayout>
    <Projects />
  </NuxtLayout>
</template>

<script lang="ts" setup>
import Projects from '~/features/admin/Projects/index.vue'

definePageMeta({
  layout: 'admin',
})

useSeoMeta({
  title: routes.admin.projects.label,
})

useApp().definePage({
  title: routes.admin.projects.label,
  breadcrumbs: [routes.admin.projects],
  sub_title: 'Manage Ministries/Departments Name list',
})
</script>
