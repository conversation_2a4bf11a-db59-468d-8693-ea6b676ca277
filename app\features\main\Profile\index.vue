<template>
  <Container>
    <div class="my-8">
      <div class="mb-1 text-3xl">
        บัญชีของฉัน
      </div>
      <div class="text-[#475467]">
        My Account
      </div>
    </div>
    <Form @submit="onSubmit">
      <Card>
        <div class="mb-5 font-bold">
          ข้อมูลส่วนตัว (Personal Info.)
        </div>
        <div class="flex gap-[92px]">
          <img
            src="/profile_mock.png"
            class="h-[230px] w-[230px]"
            alt="profile_mock"
          />

          <FormFields
            class="w-full max-w-[516px]"
            :options="fieldForm"
          />
          <!-- <Separator class="my-5" /> -->
          <!-- <div class="flex justify-end">
            <Button
              label="ตกลง"
              type="submit"
            />
          </div> -->
        </div>
      </Card>
      <Card class="mt-6">
        <div class="mb-5 font-bold">
          ข้อมูลทีม (Team Info.)
        </div>
        <FormFields

          class="grid w-full gap-4 md:grid-cols-2"
          :options="fieldRole"
        />
      </Card>
      <Separator class="my-6" />
      <div class="flex justify-end gap-4">
        <Button
          label="Cancel"
          variant="outline"
          color="neutral"
          :to="routes.home.to"
        />
        <Button
          label="Save Changes"
          type="submit"
        />
      </div>
    </Form>
  </Container>
</template>

<script lang="ts" setup>
import { routes } from '~/constants/routes'
import { TEAM_OPTIONS } from '~/constants/team'

const router = useRouter()
const form = useForm({
  validationSchema: toTypedSchema(v.object({
    email: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณาระบุ Email')), ''),
    fullName: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณาระบุชื่อเต็ม')), ''),
    nickName: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณาระบุชื่อเล่น')), ''),
    team: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณาเลือกทีม')), ''),
    role: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณาระบุตำแหน่ง')), ''),
  })),
  initialValues: {
    email: '<EMAIL>',
    fullName: 'Kanittha Inbumrung',
    nickName: 'Kwang',
    team: 'Manager Team',
    role: 'Development Manager',
  },
})

const fieldForm = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'อีเมล (Email)',
      name: 'email',
      disabled: true,
    },
  },

  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'ชื่อเต็ม (Full Name)',
      name: 'fullName',
      disabled: true,
    },
  },

  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'ชื่อเล่น (Display Name)',
      name: 'nickName',
    },
  },
])

const fieldRole = createFormFields(() => [
  {
    type: INPUT_TYPES.SELECT,
    props: {
      label: 'ทีม (Team)',
      name: 'team',
      options: TEAM_OPTIONS,
    },
  },

  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'ตำแหน่ง (Position)',
      name: 'role',
    },
  },

])

const onSubmit = form.handleSubmit((values) => {
  router.push(routes.home.to)
})
</script>
