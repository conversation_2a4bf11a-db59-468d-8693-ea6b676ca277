<template>
  <Form
    @submit="onSubmit"
  >
    <div class="mb-9 flex justify-between gap-[92px]">
      <img
        src="/profile_mock.png"
        class="h-[230px] w-[230px]"
        alt="profile_mock"
      />
      <FormFields
        class="w-full max-w-[516px]"
        :options="fieldForm"
      />
    </div>

    <FormFields
      class="grid w-full gap-4 md:grid-cols-2"
      :options="fieldRole"
    />
    <Separator class="mt-4 mb-5" />
    <div class="flex justify-end gap-4">
      <Button
        label="Save Changes"
        type="submit"
      />
    </div>
  </Form>
</template>

<script lang="ts" setup>
const noti = useNotification()

const form = useForm({
  validationSchema: toTypedSchema(v.object({
    email: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณาระบุ Email')), ''),
    fullName: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณาระบุชื่อเต็ม')), ''),
    nickName: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณาระบุชื่อเล่น')), ''),
    team: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณาเลือกทีม')), ''),
    role: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณาระบุตำแหน่ง')), ''),
  })),
  initialValues: {
    email: '<EMAIL>',
    fullName: 'Kanittha Inbumrung',
    nickName: 'Kwang',
    team: 'Manager Team',
    role: 'Development Manager',
  },
})

const fieldForm = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'อีเมล (Email)',
      name: 'email',
      disabled: true,
    },
  },

  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'ชื่อเต็ม (Full Name)',
      name: 'fullName',
      disabled: true,
    },
  },

  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'ชื่อเล่น (Display Name)',
      name: 'nickName',
    },
  },
])

const fieldRole = createFormFields(() => [
  {
    type: INPUT_TYPES.SELECT,
    props: {
      label: 'ทีม (Team)',
      name: 'team',
      options: TEAM_OPTIONS,
    },
  },

  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'ตำแหน่ง (Position)',
      name: 'role',
    },
  },

])

const onSubmit = form.handleSubmit((values) => {
  noti.success({
    title: 'Save Changes',
    description: 'Item updated successfully.',
  })
})
</script>
