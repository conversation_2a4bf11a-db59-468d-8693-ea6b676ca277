export const getWorkingDaysInMonth = (year: number, month: number, holidays: string[]) => {
  const start = new Date(year, month, 1)
  const end = new Date(year, month + 1, 0)
  let workingDays = 0

  while (start <= end) {
    if (!isWeekendOrHoliday(start, holidays)) workingDays++
    start.setDate(start.getDate() + 1)
  }

  return workingDays
}

const isWeekendOrHoliday = (date: Date, holidays: string[]) => {
  const day = date.getDay()
  const formattedDate = date.toISOString()?.split('T')[0] || ''

  return day === 0 || day === 6 || (formattedDate && holidays.includes(formattedDate))
}
