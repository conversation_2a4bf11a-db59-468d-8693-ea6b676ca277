<template>
  <NuxtLayout>
    <SummaryReport />
  </NuxtLayout>
</template>

<script lang="ts" setup>
import SummaryReport from '~/features/timesheetAdmin/SummaryReport/index.vue'

definePageMeta({
  layout: 'timesheet-admin',
})

useSeoMeta({
  title: routes.adminTimesheet.summaryReport.label,
})

useApp().definePage({
  title: routes.adminTimesheet.summaryReport.label,
  breadcrumbs: [routes.adminTimesheet.summaryReport],
  sub_title: 'Summary Report',
})
</script>
