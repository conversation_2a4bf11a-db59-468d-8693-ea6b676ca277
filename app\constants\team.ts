export const enum TEAM {
  MANAGEMENT = 'MANAGEMENT',
  MANAGER = 'MANAGER',
  FINANCE = 'FN',
  DEVELOPER = 'DEV',
  ANALYST = 'ANL',
  UX = 'UX',
  SPECIAL = 'SPC',
  INNOVATION = 'INNO',
  BIZDEV = 'BD',
  OUTSOURCE = 'OUT',
  ADMIN = 'ADMIN',
  ACCOUNT = 'ACCOUNT',
  WORKSPACE = 'WS',
  TESTER = 'TESTER',
  LEGAL = 'LEGAL',
  BIZCO = 'BC',
  PEOPLE = 'PP',
  ASSIST = 'PA',
  AUDIT = 'IA',
  PM = 'PM',
  SALES = 'AE',
  IT = 'IT',
  SOLUTION = 'SOL',
  SUPPORT = 'SUP',
  DEVOPS = 'DEVOPS',
  PRESALES = 'PS',
}

export const TEAM_LABEL = {
  [TEAM.MANAGEMENT]: 'Management Team',
  [TEAM.MANAGER]: 'Manager Team',
  [TEAM.FINANCE]: 'Finance Team',
  [TEAM.DEVELOPER]: 'Developer Team',
  [TEAM.ANALYST]: 'Analyst Team',
  [TEAM.UX]: 'UX Team',
  [TEAM.SPECIAL]: 'Special Team',
  [TEAM.INNOVATION]: 'Innovation Team',
  [TEAM.BIZDEV]: 'Business Development Team',
  [TEAM.OUTSOURCE]: 'Outsource',
  [TEAM.ADMIN]: 'Admin Team',
  [TEAM.ACCOUNT]: 'Accountant Team',
  [TEAM.WORKSPACE]: 'Workspace Team',
  [TEAM.TESTER]: 'Tester Team',
  [TEAM.LEGAL]: 'Legal Team',
  [TEAM.BIZCO]: 'Bizco Team',
  [TEAM.PEOPLE]: 'People Partnership Team',
  [TEAM.ASSIST]: 'Personal Assistant Team',
  [TEAM.AUDIT]: 'Internal Audit / Process Team',
  [TEAM.PM]: 'Project Manager Team',
  [TEAM.SALES]: 'Sales Team',
  [TEAM.IT]: 'IT Support Team',
  [TEAM.SOLUTION]: 'Solution Team',
  [TEAM.SUPPORT]: 'Support Team',
  [TEAM.DEVOPS]: 'DevOps Team',
  [TEAM.PRESALES]: 'Presales Engineer Team',
}

// Dynamically generate TEAM_OPTIONS from TEAM_LABEL
export const TEAM_OPTIONS = Object.entries(TEAM_LABEL).map(([value, label]) => ({
  value,
  label,
}))
