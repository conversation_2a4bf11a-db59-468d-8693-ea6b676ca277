import { defineEventHand<PERSON>, getQuery } from 'h3'

export default defineEventHandler((event) => {
  const query = getQuery(event)
  const {
    limit, page,
  } = query

  const items: any = [
    {
      date: '2025-07-01',
      created_at: '2025-07-01T00:51:13.291186+00:00',
      user_id: '432b6453-e090-45e8-b240-fd8b8d22bb01',
      checkin_type: 'OFFICE_HQ',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'ADMIN',
        email: '<EMAIL>',
        avatar_url: null,
        display_name: '<EMAIL>',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T00:57:00.944001+00:00',
      user_id: 'fec397a7-7669-42af-b683-c23e20075171',
      checkin_type: 'OFFICE_HQ',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'PP',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2025-04-10/8757374894256_133813ed83e1893d110d_512.png',
        display_name: 'Jiraporn Sadsaengchan',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T01:01:26.069004+00:00',
      user_id: '966ec8ba-193f-4ef3-ab31-7e6e5887f8f3',
      checkin_type: 'OFFICE_HQ',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'PS',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2025-03-18/8611897768742_5db79a1d3031b438cc8a_512.jpg',
        display_name: 'Sakaowrat Chuaychu',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T01:11:51.465101+00:00',
      user_id: '6166f7ee-d2e1-4431-a4a5-1ca274a4ed2c',
      checkin_type: 'OFFICE_HQ',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'BC',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2024-07-21/7452034871077_06dc4df3c169a2ffbcec_512.png',
        display_name: 'Supanaree Mankong',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T01:12:19.719772+00:00',
      user_id: '35bca82a-3d0f-42ec-ac9e-78682c9f1ee1',
      checkin_type: 'onsite',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: 'NT',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'BD',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2024-12-05/8132135811860_c3809b36c6bcbec3fb0c_512.png',
        display_name: 'Nithi Nitiyarom',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T01:12:19.719772+00:00',
      user_id: '35bca82a-3d0f-42ec-ac9e-78682c9f1ee1',
      checkin_type: 'OFFICE_HQ',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'BD',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2024-12-05/8132135811860_c3809b36c6bcbec3fb0c_512.png',
        display_name: 'Nithi Nitiyarom',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T01:12:22.765948+00:00',
      user_id: 'a9b7ba94-c14c-4510-9aa9-707c8329f591',
      checkin_type: 'OFFICE_HQ',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'FN',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2025-05-27/8956098178434_1693080a6c56ce444508_512.png',
        display_name: 'Apiradee Toithong',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T01:18:01.303655+00:00',
      user_id: '51111b8a-78f5-435e-9bad-1d5908eeb9b8',
      checkin_type: 'OFFICE_HQ',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'PP',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2025-01-27/8354537542370_5cfc0136ab48c0f40890_512.png',
        display_name: 'Malairat Sawangrattanakun',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T01:20:12.7668+00:00',
      user_id: 'b4280a11-0a51-471e-92e7-d08e33091fc7',
      checkin_type: 'onsite',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: 'NT',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'SOL',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2025-04-01/8684198061925_d15040df7c2e73bd589a_512.jpg',
        display_name: 'Narongwit Khamso',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T01:22:11.239924+00:00',
      user_id: '26e4d794-92cf-4ff2-a2d8-2acd8f180854',
      checkin_type: 'onsite',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: 'KBV',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'MANAGER',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2024-01-29/6537266463542_998376d3437bcef962d1_512.png',
        display_name: 'Chatchai Chanvej',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-02T05:11:44.850017+00:00',
      user_id: '8d06b5c3-de52-4456-9b07-9baf9420b541',
      checkin_type: 'onsite',
      original_timestamp: '2025-07-02',
      leave_period: '',
      location: 'Utimaco event',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'INNO',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2020-03-14/989085204643_b4f9b26b21920ed383a1_512.jpg',
        display_name: 'Nuttawut Kongsuwan',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T01:23:19.930888+00:00',
      user_id: '3e398f4e-db73-4157-ac2a-3f9dd0e98291',
      checkin_type: 'WFH',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'DEV',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2024-03-28/6872364349621_0fd29a6222783d5489d1_512.png',
        display_name: 'Kanittha Inbumrung',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T01:52:12.208024+00:00',
      user_id: '928bcb34-d924-4949-9578-bbbe02a73914',
      checkin_type: 'OFFICE_HQ',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'SUP',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2025-02-24/8496483191557_1d011496b6d83dd60c80_512.jpg',
        display_name: 'Supattra Ploydang',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T01:26:12.946571+00:00',
      user_id: 'f3b0d51a-a6af-4f3e-937f-f9a7059b35d1',
      checkin_type: 'WFH',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'PM',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2024-11-01/7969117395027_3c40fd7c7c822f95cfaa_512.png',
        display_name: 'Natsuda Fuknil',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T01:27:49.829202+00:00',
      user_id: '792f46f8-654f-47c9-8b0e-4cbf1e90698b',
      checkin_type: 'OFFICE_HQ',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'ACCOUNT',
        email: '<EMAIL>',
        avatar_url: null,
        display_name: '<EMAIL>',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T01:30:48.22021+00:00',
      user_id: '1a26773f-06cf-41b1-8e38-0d1e62c7f60b',
      checkin_type: 'OFFICE_HQ',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'WS',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2025-01-30/8385341425585_4390c4f3e43598b940a5_512.jpg',
        display_name: 'Narapon Hasuktrakun',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T01:33:48.726624+00:00',
      user_id: 'd7d153fb-3fde-4411-9222-9004127340c0',
      checkin_type: 'WFH',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'PM',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2025-02-03/8375387887463_0f5cc1b7e9968121bc24_512.png',
        display_name: 'Thinnakorn Chaiyapa',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T01:33:48.726624+00:00',
      user_id: 'd7d153fb-3fde-4411-9222-9004127340c0',
      checkin_type: 'onsite',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: 'DBD',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'PM',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2025-02-03/8375387887463_0f5cc1b7e9968121bc24_512.png',
        display_name: 'Thinnakorn Chaiyapa',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T01:35:16.71151+00:00',
      user_id: '075ada26-79e1-401c-8058-da82350d4be4',
      checkin_type: 'OFFICE_HQ',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'PP',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2025-06-16/9054506641844_ce77dcdcb25ead85a6fe_512.jpg',
        display_name: 'Natkrita Pojvijitr',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T01:35:54.715573+00:00',
      user_id: 'eb27cd7b-7f17-4c9f-b743-3554e77f1014',
      checkin_type: 'WFH',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'ANL',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2024-05-06/7064878761046_287312d4344384f64d49_512.png',
        display_name: 'Pichchaporn Suriyanchaicharoen',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T01:36:16.014436+00:00',
      user_id: '0a11b531-b6cd-483d-bcfe-f6f1871144eb',
      checkin_type: 'OFFICE_HQ',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'PA',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2025-04-21/8784465347796_2b5558e23cba37fc53b6_512.png',
        display_name: 'Pattaranit Rojanatemsak',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T02:06:39.882604+00:00',
      user_id: '800b1efd-86b5-4746-820a-b69c57a8f188',
      checkin_type: 'WFH',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'UX',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2022-06-01/3605237082306_1c16507cd301c1f6f833_512.png',
        display_name: 'Tunchanok Boonchuay',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T02:00:53.407572+00:00',
      user_id: '0d5a529d-1d8f-4ae7-b6ca-377b5f1ce8ad',
      checkin_type: 'OFFICE_HQ',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'ACCOUNT',
        email: '<EMAIL>',
        avatar_url: null,
        display_name: '<EMAIL>',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T19:12:38.027873+00:00',
      user_id: 'f07f9a59-9abc-4912-8e59-1c081f16427a',
      checkin_type: 'onsite',
      original_timestamp: '2025-07-02',
      leave_period: '',
      location: 'IDC',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'MANAGEMENT',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2020-01-31/930395801620_407129cd9e04e9866d6d_512.png',
        display_name: 'Sarawuth Rungcharoenkit',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T19:12:38.027873+00:00',
      user_id: 'f07f9a59-9abc-4912-8e59-1c081f16427a',
      checkin_type: 'OFFICE_AKV',
      original_timestamp: '2025-07-02',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'MANAGEMENT',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2020-01-31/930395801620_407129cd9e04e9866d6d_512.png',
        display_name: 'Sarawuth Rungcharoenkit',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T06:51:06.897475+00:00',
      user_id: '512bf51e-c55d-4a0a-9391-ccc4df00e614',
      checkin_type: 'onsite',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: 'Geneva',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'MANAGEMENT',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2019-11-28/852800784901_0cb8c910882d9234b37e_512.jpg',
        display_name: 'Pakorn Leesakul',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T06:38:31.419422+00:00',
      user_id: '1b993c8c-5c6e-4f87-ae96-7f74e8a66f94',
      checkin_type: 'Sick Leave',
      original_timestamp: '2025-07-01',
      leave_period: 'HALF_AFTERNOON',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'BC',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2024-08-05/7519378350646_ef9e0499428e9d8bc198_512.png',
        display_name: 'Muttawan Mengchuay',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T06:38:31.419422+00:00',
      user_id: '1b993c8c-5c6e-4f87-ae96-7f74e8a66f94',
      checkin_type: 'OFFICE_HQ',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'BC',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2024-08-05/7519378350646_ef9e0499428e9d8bc198_512.png',
        display_name: 'Muttawan Mengchuay',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T02:54:46.175029+00:00',
      user_id: '1b993c8c-5c6e-4f87-ae96-7f74e8a66f94',
      checkin_type: 'OFFICE_HQ',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: true,
      remark: null,
      profiles: {
        team: 'BC',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2024-08-05/7519378350646_ef9e0499428e9d8bc198_512.png',
        display_name: 'Muttawan Mengchuay',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T06:30:30.513923+00:00',
      user_id: 'b1609bc8-16b7-461f-a6b8-76dff01e7aa4',
      checkin_type: 'onsite',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: 'Toyota',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'BD',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2025-03-14/8612207468257_234fbaa586a4c9fcd8d4_512.jpg',
        display_name: 'Chitsanupong Boonma',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T06:30:30.513923+00:00',
      user_id: 'b1609bc8-16b7-461f-a6b8-76dff01e7aa4',
      checkin_type: 'OFFICE_HQ',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'BD',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2025-03-14/8612207468257_234fbaa586a4c9fcd8d4_512.jpg',
        display_name: 'Chitsanupong Boonma',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T05:57:28.328201+00:00',
      user_id: 'a59f4641-c61c-4c81-90aa-012ae024bf8a',
      checkin_type: 'onsite',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: 'DDC',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'BD',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2025-04-25/8808134596002_4e6ef6981de569e8f570_512.jpg',
        display_name: 'Phuwamate leangwarapornleart',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T05:57:28.328201+00:00',
      user_id: 'a59f4641-c61c-4c81-90aa-012ae024bf8a',
      checkin_type: 'onsite',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: 'MOPH',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'BD',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2025-04-25/8808134596002_4e6ef6981de569e8f570_512.jpg',
        display_name: 'Phuwamate leangwarapornleart',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T01:53:02.133478+00:00',
      user_id: 'a59f4641-c61c-4c81-90aa-012ae024bf8a',
      checkin_type: 'OFFICE_HQ',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: true,
      remark: null,
      profiles: {
        team: 'BD',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2025-04-25/8808134596002_4e6ef6981de569e8f570_512.jpg',
        display_name: 'Phuwamate leangwarapornleart',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T05:30:52.875637+00:00',
      user_id: 'eeb53a02-2a89-45ae-a662-5766a1e7b938',
      checkin_type: 'WFH',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'SPC',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2022-02-18/3122930923765_c6bfab807ab673b8c53d_512.jpg',
        display_name: 'Sammotic Switchyarn',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T04:54:58.672484+00:00',
      user_id: '6695038c-74a9-4627-a818-a9c0d62d1104',
      checkin_type: 'onsite',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: 'DBD',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'ANL',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2025-06-02/8987381900820_31583ee605620fae8ba0_512.jpg',
        display_name: 'Jakkrapat Santawaja',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T04:52:40.969355+00:00',
      user_id: '1f234a58-cf05-44bc-8c7f-48d81f5089ec',
      checkin_type: 'WFH',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'DEV',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2024-09-27/7779917133511_a18321d3c1a1e000134a_512.png',
        display_name: 'Ittipat Pattum',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T04:49:07.184415+00:00',
      user_id: 'f7dd750e-0a1a-48fa-9b51-82773ece83bf',
      checkin_type: 'WFH',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'DEV',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2025-03-10/8579545998115_c9953d909abc98421e55_512.jpg',
        display_name: 'Kiattisak Wanrakcharoenrung',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T04:30:39.01558+00:00',
      user_id: '8c1a819a-7d0e-4555-9398-34253f1a2bd7',
      checkin_type: 'WFH',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'INNO',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2022-05-05/3478342074870_d30ea392b7c5f656598c_512.png',
        display_name: 'Wiphawee Maneengarm',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T01:51:57.616581+00:00',
      user_id: 'e03ba8c8-75ee-4851-b0ec-9e6b05fdcff4',
      checkin_type: 'OFFICE_HQ',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'ACCOUNT',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2023-06-01/5352658109413_4cc63d1a950b64c41efc_512.jpg',
        display_name: 'Krittana',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T04:28:29.155715+00:00',
      user_id: 'e8d2c403-39b9-465d-8165-7553ecf5bfd5',
      checkin_type: 'WFH',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'INNO',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2025-07-07/9143307592951_c83388e39aca7b323610_512.png',
        display_name: 'Pratchaya Maneechot',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T01:58:03.683489+00:00',
      user_id: 'b333f849-3755-444f-aea0-c9f34c9de2ce',
      checkin_type: 'WFH',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'SOL',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2025-06-03/8992793562674_d0341b9209c57affb4d0_512.jpg',
        display_name: 'Jirayu Khotprathum',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T04:10:24.914754+00:00',
      user_id: '0f236500-57b7-4135-9972-81e39b463bd5',
      checkin_type: 'Sick Leave',
      original_timestamp: '2025-07-01',
      leave_period: 'FULL_DAY',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'ANL',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2025-03-26/8659301451714_a3037d338e5f7604cdde_512.png',
        display_name: 'Benyapha Theanlow',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T04:05:08.016142+00:00',
      user_id: '127c4311-d2e1-476e-be88-1715d4a74f87',
      checkin_type: 'onsite',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: 'SME',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'PM',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2025-05-17/8896377089463_9b7cb74efcf48cd6f0c0_512.jpg',
        display_name: 'Supattra Yordthern',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T04:05:02.504055+00:00',
      user_id: '65717459-1436-41b8-b6f7-a1c988dc8c31',
      checkin_type: 'WFH',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'TESTER',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2024-06-04/7242536241408_0338033b4f7c3ef40e67_512.png',
        display_name: 'Thanatat Chatawaraha',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T04:00:29.785175+00:00',
      user_id: '74976381-9632-4aed-9f37-22aad1f83c86',
      checkin_type: 'WFH',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'TESTER',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2023-05-31/5333352321479_70786d0a20524de5ded5_512.jpg',
        display_name: 'Chaitanan Suphasinwongchai',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T03:56:02.943508+00:00',
      user_id: '375ee390-2aee-4de5-a194-016217de5ade',
      checkin_type: 'WFH',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'DEV',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2022-07-25/3845657848258_d62c53c1bb04f375b034_512.jpg',
        display_name: 'Suchart Sanluang',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T03:55:21.915799+00:00',
      user_id: '9ffe3336-2acd-4edf-9749-9e7201981d13',
      checkin_type: 'OFFICE_AKV',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'BD',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2025-02-07/8415799882082_cbdf2e10bcfc826830c1_512.jpg',
        display_name: 'Wint Hmone Thant',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T03:54:37.997434+00:00',
      user_id: '277f5dd5-96dc-4739-91c2-9b458ad70a47',
      checkin_type: 'WFH',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'UX',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2024-04-03/6914876423649_d0ebb0fc9339b3b98b31_512.jpg',
        display_name: 'Nattaporn Noppadol',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T03:53:31.70435+00:00',
      user_id: '8686ef69-27f3-4522-952a-80c7a57188e9',
      checkin_type: 'WFH',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'INNO',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2023-07-25/5633929424852_ba18a37b2eafd5f95e8e_512.jpg',
        display_name: 'Kriskanin Hengniran',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T03:50:47.333776+00:00',
      user_id: '17222bde-9184-4a3d-9a72-266c5efdd73b',
      checkin_type: 'WFH',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'DEV',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2023-05-02/5197128893202_eb112d1304e303195d03_512.png',
        display_name: 'Taweewat Phimhin',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T03:48:46.800242+00:00',
      user_id: 'b1455eb9-1f75-4331-bd8a-3a4c31ec6bce',
      checkin_type: 'WFH',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'DEV',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2025-04-01/8684215229173_7a2d8871f438626b37fe_512.jpg',
        display_name: 'Nanpipat Klinpratoom',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T03:42:55.682165+00:00',
      user_id: '6f9c5d22-8b75-4bb1-bd6a-20033a943b4f',
      checkin_type: 'WFH',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'UX',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2023-05-24/5337025788672_cd34cabc41e85bde3763_512.png',
        display_name: 'Athiwat Chotimaneewatthikorn',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T03:35:52.346155+00:00',
      user_id: 'd19a18a4-fa6a-44a8-8c07-61f8de262243',
      checkin_type: 'onsite',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: 'SME',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'SOL',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2025-02-10/8426273985826_7fd57cda189214bfaf6b_512.jpg',
        display_name: 'Natthapong Punwongrach',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T03:34:06.287532+00:00',
      user_id: '912093b1-f8bf-4774-b326-40a65cde63fa',
      checkin_type: 'WFH',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'DEV',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2023-05-22/5312482766081_1670716410f6ae6dddd7_512.png',
        display_name: 'Theeranan Treesuk',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T03:30:23.744359+00:00',
      user_id: 'a80af83d-b09a-44a1-af45-ed27b4c39f46',
      checkin_type: 'WFH',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'UX',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2025-03-16/8608623788981_102768c05c9b76d84020_512.png',
        display_name: 'Nithita Hempaisanpipat',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T01:40:40.627406+00:00',
      user_id: 'ad882acb-62ef-4927-99a8-f40f1d919d2d',
      checkin_type: 'WFH',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'DEV',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2025-05-05/8843867072837_293c6e74e0ed953e8934_512.jpg',
        display_name: 'Sahadsanai Poomprasert',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T03:24:18.407443+00:00',
      user_id: '68ab1294-34b3-4d81-8a97-0dff60333ec6',
      checkin_type: 'OFFICE_HQ',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'BD',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2023-12-19/6388599143088_e6a879b55cb4284211a2_512.png',
        display_name: 'Catherine Ssepuuya Nabbala',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T03:21:27.484987+00:00',
      user_id: '1b246c24-f7c7-408f-a209-32c7f54f68c7',
      checkin_type: 'OFFICE_HQ',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'AE',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2023-12-19/6366844612403_17ec40cbf1bfa94c364b_512.png',
        display_name: 'Arpassaporn Ratnaratorn',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T03:21:27.484987+00:00',
      user_id: '1b246c24-f7c7-408f-a209-32c7f54f68c7',
      checkin_type: 'onsite',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: 'DBD',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'AE',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2023-12-19/6366844612403_17ec40cbf1bfa94c364b_512.png',
        display_name: 'Arpassaporn Ratnaratorn',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T03:17:42.010759+00:00',
      user_id: '9c137b8a-a911-4dd1-bc68-7d1c11c8ad5d',
      checkin_type: 'WFH',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'TESTER',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2024-12-16/8178073559730_fa4f1877b4706f9eca7c_512.png',
        display_name: 'Siranon Kaewmon',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T03:11:01.52396+00:00',
      user_id: 'cf310519-b00a-449b-b396-5f4174b3390a',
      checkin_type: 'OFFICE_HQ',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'BD',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2025-05-07/8861084597460_45ffdf329345cfdc6b2d_512.jpg',
        display_name: 'noumi siwar',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T03:07:18.533748+00:00',
      user_id: '5a95a584-69cf-40ac-8ec2-b9f8219ca57d',
      checkin_type: 'WFH',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'DEV',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2023-09-25/5968557597936_c750902e3ffc4f690d80_512.jpg',
        display_name: 'Passakon Puttasuwan',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T03:04:21.255149+00:00',
      user_id: '309ce25d-d55b-4cfe-a11a-7cbda888ba54',
      checkin_type: 'onsite',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: 'DBD',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'ANL',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2023-09-15/5898997174309_8f8f8c26cdbddbd63ea4_512.png',
        display_name: 'Benjarat Chantaprasert',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T03:04:04.673529+00:00',
      user_id: '3487fc13-baea-40ce-be8c-f107cb3989e8',
      checkin_type: 'WFH',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'TESTER',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2023-07-03/5515423124549_25451c8288bad6af1e6a_512.jpg',
        display_name: 'Chaloemkiat Saengsrijan',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T01:59:04.223444+00:00',
      user_id: 'ee9d5984-c5e2-4a3c-a403-9c5456e60459',
      checkin_type: 'OFFICE_HQ',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'LEGAL',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2025-06-09/9003743532183_682d5ca76c17baccdf10_512.jpg',
        display_name: 'Kanyarat Rattanabureewong',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T03:03:21.753894+00:00',
      user_id: '368b69ff-8e9e-4973-b3cc-212292362c54',
      checkin_type: 'WFH',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'ANL',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2024-11-21/8063355056867_539891fa4ba718a6b3c8_512.png',
        display_name: 'Pairoj Kerdsuk',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T03:03:14.258367+00:00',
      user_id: '064269c4-d51a-4be2-bfb4-9b62e61c144e',
      checkin_type: 'OFFICE_HQ',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'SOL',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2025-07-01/9123924980086_3992659f848e5c96ba57_512.jpg',
        display_name: 'buck',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T03:02:08.861958+00:00',
      user_id: '9bde73bf-0451-4c10-acdd-0079b623610c',
      checkin_type: 'OFFICE_HQ',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'IT',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2025-07-22/9221467889943_09b4f3bdbddcc2dd7be3_512.jpg',
        display_name: 'Sarawuth Lueangthongkham',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T03:01:08.6528+00:00',
      user_id: 'f39ad9ea-eeac-47c9-b42a-e1ec4d20c10d',
      checkin_type: 'WFH',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'DEV',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2025-05-02/8860290470208_716e4463416f61ac6e5e_512.jpg',
        display_name: 'Shin',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T02:52:29.43249+00:00',
      user_id: '0c5efef8-4f8b-413b-8873-dd4e5cad4216',
      checkin_type: 'OFFICE_HQ',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'MANAGER',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2025-05-15/8892711961523_723c995df7bd83c264b5_512.png',
        display_name: 'Jakkit Phromdaeng',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T02:51:29.950361+00:00',
      user_id: 'e6c379e3-5a65-4dcb-8eda-060392c23c1b',
      checkin_type: 'OFFICE_HQ',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'ACCOUNT',
        email: '<EMAIL>',
        avatar_url: null,
        display_name: 'Achara Ananchaisin',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T02:50:52.401678+00:00',
      user_id: '002cf8f4-d559-4469-a89e-310729bbdf78',
      checkin_type: 'OFFICE_HQ',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'LEGAL',
        email: '<EMAIL>',
        avatar_url: null,
        display_name: '<EMAIL>',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T02:47:59.367278+00:00',
      user_id: '9079de96-1905-4bb6-8814-8de1f51cea8e',
      checkin_type: 'WFH',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'DEV',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2025-03-19/**********419_5bb3f17259a12c9a79e4_512.jpg',
        display_name: 'Nonchana Sangsom',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T02:39:00.134217+00:00',
      user_id: 'c242a889-f3cd-4751-9f6f-440d5965a14b',
      checkin_type: 'WFH',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'DEV',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2020-12-14/1568789061573_3ed947a36bb52deebe27_512.jpg',
        display_name: 'Jakkapong Promsana',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T02:33:51.018609+00:00',
      user_id: '89088ee8-1e64-499c-b633-75b3964e6328',
      checkin_type: 'OFFICE_HQ',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'LEGAL',
        email: '<EMAIL>',
        avatar_url: null,
        display_name: '<EMAIL>',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T02:33:51.018609+00:00',
      user_id: '89088ee8-1e64-499c-b633-75b3964e6328',
      checkin_type: 'WFH',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'LEGAL',
        email: '<EMAIL>',
        avatar_url: null,
        display_name: '<EMAIL>',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T02:28:24.875289+00:00',
      user_id: 'dda8387a-7704-4a12-ae6c-d1e92939d0fe',
      checkin_type: 'OFFICE_HQ',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'FN',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2021-03-30/1924708078337_3ca2551deb53f1be6268_512.png',
        display_name: 'Supawee Suwaprichapas',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T02:25:51.712344+00:00',
      user_id: '05ec73b1-9425-4b80-9c30-b872f56d3850',
      checkin_type: 'OFFICE_HQ',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'AE',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2024-11-04/7960974216471_be23e8788385589671d7_512.png',
        display_name: 'Valavee Apichatrojanakul',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T01:58:03.683489+00:00',
      user_id: 'b333f849-3755-444f-aea0-c9f34c9de2ce',
      checkin_type: 'OFFICE_HQ',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'SOL',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2025-06-03/8992793562674_d0341b9209c57affb4d0_512.jpg',
        display_name: 'Jirayu Khotprathum',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T02:25:51.712344+00:00',
      user_id: '05ec73b1-9425-4b80-9c30-b872f56d3850',
      checkin_type: 'onsite',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: 'NT',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'AE',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2024-11-04/7960974216471_be23e8788385589671d7_512.png',
        display_name: 'Valavee Apichatrojanakul',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T23:40:52.460017+00:00',
      user_id: 'e03ba8c8-75ee-4851-b0ec-9e6b05fdcff4',
      checkin_type: 'Annual Leave',
      original_timestamp: '2025-07-02',
      leave_period: 'FULL_DAY',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'ACCOUNT',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2023-06-01/5352658109413_4cc63d1a950b64c41efc_512.jpg',
        display_name: 'Krittana',
      },
    },
    {
      date: '2025-07-01',
      created_at: '2025-07-01T02:18:16.767519+00:00',
      user_id: '3a7f996b-6dea-45b9-ae16-2dfe0e6308de',
      checkin_type: 'OFFICE_HQ',
      original_timestamp: '2025-07-01',
      leave_period: '',
      location: '',
      is_unused: false,
      remark: null,
      profiles: {
        team: 'IT',
        email: '<EMAIL>',
        avatar_url: 'https://avatars.slack-edge.com/2025-02-26/8497993389175_df6ce5d513eb519bcd7d_512.png',
        display_name: 'Natthapat Singhanukunkit',
      },
    },
  ].map((value) => ({
    value,
    sort: Math.random(),
  }))
    .sort((a, b) => a.sort - b.sort)
    .map(({
      value,
    }) => value)

  return {
    page: +(page || 1),
    total: 200,
    limit: +(limit || 30),
    count: items.length,
    items,
  }
})
