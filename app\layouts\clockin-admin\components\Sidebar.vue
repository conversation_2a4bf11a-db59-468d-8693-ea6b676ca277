<template>
  <aside
    :class="[`
      flex h-full flex-col overflow-y-auto bg-white
      transition-all duration-300
    `]"
  >
    <div
      :class="['flex h-[72px] items-center p-5', {
        'justify-center': isCollapsed,
        'justify-between': !isCollapsed,
      }]"
    >
      <NuxtLink :to="routes.admin.users.to">
        <img
          v-if="!isCollapsed"
          src="/logo-mini.png"
          alt="logo_mini_color"
          class="h-[27px]"
        />
      </NuxtLink>

      <Icon
        name="mingcute:dot-grid-line"
        class="size-[24px] cursor-pointer text-gray-500"
      />
    </div>
    <div class="flex items-center justify-between bg-[#7A5AF8] px-7 py-4 font-bold text-white">
      {{ isCollapsed ? '' : 'Super Admin' }}

      <Icon
        v-if="!isCollapsed"
        name="fluent:arrow-circle-left-24-regular"
        class="size-[24px] cursor-pointer"
        @click="$emit('toggle-collapsed')"
      />
      <Icon
        v-else
        name="fluent:arrow-circle-right-24-regular"
        class="size-[24px] cursor-pointer"
        @click="$emit('toggle-collapsed')"
      />
    </div>

    <div class="flex-1 overflow-y-auto p-5">
      <NavigationMenu
        orientation="vertical"
        :items="navigationItems"
        :collapsed="isCollapsed"
        :popover="isCollapsed"
        :tooltip="isCollapsed"
        :ui="{
          list: 'space-y-2 ',
          label: [
            'text-base text-gray-500 font-normal py-[12px] px-[10px] rounded-lg',
            'hover:text-primary',
          ],
          link: [
            'cursor-pointer text-base text-gray-500 font-normal  px-[10px] rounded-lg gap-3',
            ' hover:text-primary',
            'data-active:before:bg-white data-active:before:rounded-lg data-active:text-primary font-semibold',
          ],
          linkLeadingIcon: 'group-data-[state=open]:text-current text-current size-[24px] group-hover:text-primary ',
          childList: 'border-none ms-0 pl-8 bg-gray-100 mt-2 py-1 rounded-lg',
          childLink: 'ps-0',
          childItem: 'ps-0 ',
        }"
        class="w-full justify-center"
      />
    </div>
    <div
      v-if="isMobile"
      class="border-t border-gray-100 p-3"
    >
      <div class="flex items-center justify-between gap-2">
        <div class="flex min-w-0 flex-1 items-center gap-3">
          <Avatar
            class="border-muted size-[32px] flex-shrink-0 border text-lg"
            icon="ri:user-line"
          />
          <div class="flex min-w-0 flex-1 flex-col">
            <p class="truncate text-sm font-bold">
              12
            </p>
            <p class="text-muted truncate text-xs">
              33
            </p>
          </div>
        </div>
        <DropdownMenu
          arrow
          size="xl"
          :items="userMenuItems"
          :ui="{
            content: 'w-48',
          }"
        >
          <Button
            icon="ph:dots-three-outline-vertical-bold"
            variant="ghost"
            color="neutral"
            size="xs"
          />
        </DropdownMenu>
      </div>
    </div>
  </aside>
</template>

<script lang="ts" setup>
import type { NavigationMenuItem } from '@nuxt/ui'
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { sidebarAdminClockin } from '~/constants/routes'

interface Props {
  isCollapsed: boolean
  isMobile?: boolean
}

defineProps<Props>()

defineEmits<{
  'toggle-collapsed': []
}>()

const route = useRoute()

const userMenuItems = [

  {
    label: 'ออกจากระบบ',
    icon: 'i-lucide-log-out',
    to: '/api/auth/logout',
    external: true,
  },
]

const navigationItems = computed<NavigationMenuItem[]>(() => {
  return sidebarAdminClockin.map((item) => {
    let isAnyChildActive = false
    const mappedChildren = item.children?.map((child) => {
      const isChildCurrentlyActive = route.path === child.to

      if (isChildCurrentlyActive) {
        isAnyChildActive = true
      }

      return {
        active: isChildCurrentlyActive,
        class: 'hover:bg-transparent hover:text-gray-700 hover:font-bold py-2 data-active:before:bg-transparent data-active:text-gray-700 data-active:font-bold',
        icon: '',
        ...child,
      }
    })

    const selfIsActive = item.to ? route.path.startsWith(String(item.to)) : false

    let itemIsActive = selfIsActive || isAnyChildActive // A root item is active if its own link matches OR if any child is active

    if (item.to === '/admin' && route.path !== '/admin') {
      itemIsActive = false // Ensure the root item is not active if the current path is not exactly '/'
    }

    const itemDefaultOpen = item.children ? isAnyChildActive : false

    return {
      ...item,
      active: itemIsActive,
      class: itemIsActive
        ? 'before:bg-primary before:rounded-lg text-white'
        : '',
      defaultOpen: itemDefaultOpen || selfIsActive,
      open: itemDefaultOpen || selfIsActive,
      children: mappedChildren,
      to: mappedChildren ? undefined : item.to,
    }
  })
})
</script>
