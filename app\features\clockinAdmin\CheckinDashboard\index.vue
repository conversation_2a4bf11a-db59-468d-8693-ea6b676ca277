<template>
  <div class="mb-6 flex flex-col gap-4">
    <Form>
      <FormFields
        class="
            grid items-end gap-x-2
            md:gap-x-4
            lg:grid-cols-2
          "
        :options="filterFields"
      />
    </Form>
    <!-- <Button
      label="Clear Filters"
      variant="ghost"
      color="neutral"
      icon="i-heroicons-x-circle"
      class="w-fit"
      @click="clearFilters"
    /> -->
    <div class="h-[200px] md:h-[300px] lg:h-[400px]">
      <Bar
        :data="chartData"
        :options="chartOptions"
      />
    </div>
    <div class="grid">
      <Table
        :options="tableOptions"
        @pageChange="checkinLoader.fetchPage"
        @search="checkinLoader.fetchSearch"
      >
        <template #checkin_type-cell="{ row }">
          <div class="flex items-center gap-1">
            <img
              :src="useGetIconCheckin(row.original)"
              alt="icon"
              class="size-6"
            />
            {{ getLabel(row.original) }}
            <div v-if="row.original.checkin_type?.endsWith('Leave')">
              ({{ row.original.leave_period?.startsWith('HALF') ? 'Half Day' : 'Full Day' }})
            </div>
            <div v-else-if="row.original.checkin_type === CHECKIN.ONSITE">
              ({{ row.original.location }})
            </div>
          </div>
        </template>
        <template #check-in-cell="{ row }">
          <Badge
            v-bind="getCheckinBadge(getCheckinStatus(row.original))"
            variant="subtle"
          />
        </template>
      </Table>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { CheckinItem } from '~/types'
import { Bar } from 'vue-chartjs'
import {
  CHECKIN_COLORS,
  CHECKIN_OPTIONS,
  CHECKIN,
} from '~/constants/checkin_types'
import { GROUP_LABELS } from '~/constants/icons'
import { TEAM_OPTIONS } from '~/constants/team'
import { useCheckinsPageLoader } from '~/loaders/admin/checkin'

const checkinLoader = useCheckinsPageLoader()

const chartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      display: false,
    },
  },
  elements: {
    bar: {
      borderWidth: 0,
      borderRadius: 4,
    },
  },
  scales: {
    y: {
      beginAtZero: true,
      ticks: {
        stepSize: 20,
      },
    },
  },
}

const form = useForm({
  validationSchema: toTypedSchema(
    v.object({
      date: v.optional(v.string(), ''),
      team: v.optional(v.array(v.string()), []),
      type: v.optional(v.string(), ''),
      search: v.optional(v.string(), ''),
    }),
  ),
})

const filterFields = createFormFields(() => [
  {
    type: INPUT_TYPES.DATE,
    class: 'col-span-1',
    props: {
      name: 'date',
      label: 'Date',
      placeholder: 'Select Date',
      enableTimePicker: false,
      autoApply: true,
      range: true,
      maxDate: new Date(),
    },
  },
  {
    type: INPUT_TYPES.SELECT_MULTIPLE,
    class: 'col-span-2 col-start-1 col-end-2',
    props: {
      name: 'team',
      label: 'Team',
      placeholder: 'Select Team',
      options: TEAM_OPTIONS,
      searchable: true,
    },
  },
  {
    type: INPUT_TYPES.SELECT,
    class: 'col-span-1',
    props: {
      name: 'type',
      label: 'Type',
      placeholder: 'Select Type',
      options: CHECKIN_OPTIONS,
      searchable: true,

    },
  },
  {
    type: INPUT_TYPES.TEXT,
    class: 'col-span-1',
    props: {
      name: 'search',
      label: 'Search',
      placeholder: 'Name',
    },
  },
])

const tableOptions = useTable<any>({
  repo: checkinLoader,
  options: {
    isHidePagination: false,
  },
  columns: () => [
    {
      accessorKey: 'date',
      header: 'Date',
      type: COLUMN_TYPES.TEXT,
    },
    {
      accessorKey: 'profiles.display_name',
      header: 'Name',
      type: COLUMN_TYPES.TEXT,
    },
    {
      accessorKey: 'checkin_type',
      header: 'Check-in type',
      type: COLUMN_TYPES.TEXT,
    },
    {
      accessorKey: 'check-in',
      header: 'Check-in',
      type: COLUMN_TYPES.TEXT,
    },
  ],
})

const chartData = computed(() => {
  const checkinCounts: Record<string, number> = {};

  (checkinLoader.fetch.items as CheckinItem[]).forEach((item: CheckinItem) => {
    const type = item.checkin_type || 'UNKNOWN'

    checkinCounts[type] = (checkinCounts[type] || 0) + 1
  })

  const labels = Object.keys(checkinCounts).map((key) => CHECKIN_LABEL[key as CheckinType] || key)

  return {
    labels,
    datasets: [
      {
        label: 'จำนวนโครงการ',
        backgroundColor: labels.map((label: string) => CHECKIN_COLORS[label] || '#9CA3AF'),
        borderColor: labels.map((label: string) => CHECKIN_COLORS[label] || '#9CA3AF'),
        borderWidth: 1,
        data: Object.values(checkinCounts),
        tooltip: {
          callbacks: {
            label: (context: any) => {
              const value = context.raw || 0

              return `โครงการที่ใช้งาน: ${value}`
            },
          },
        },
      },
    ],
  }
})

const startDate = computed(() => {
  return form.values.date && form.values.date[0] ? TimeHelper.getDateFormTime(form.values.date[0]) : ''
})

const endDate = computed(() => {
  return form.values.date && form.values.date[1] ? TimeHelper.getDateFormTime(form.values.date[1]) : ''
})

checkinLoader.fetchSetLoading()
onMounted(() => {
  fetchCheckins()
})

const getLabel = (data: CheckinItem): string => {
  if (data.checkin_type?.startsWith('OFFICE')) {
    return GROUP_LABELS[data.checkin_type] || ''
  } else if (data.checkin_type?.endsWith('Leave')) {
    const leaveType = data.checkin_type.replace('Leave', '').toLowerCase()

    return GROUP_LABELS[leaveType] ?? data.checkin_type
  }

  return GROUP_LABELS[data.checkin_type] ?? data.checkin_type
}

const getCheckinStatus = (row: any) => {
  const dateStr = TimeHelper.getDateFormTime(row.date)
  const original = TimeHelper.getDateFormTime(row.original_timestamp)
  const isEdited = row.is_unused === true

  if (dateStr >= original) {
    if (isEdited) return 'On-time (Edited)'

    return 'On-time'
  }

  if (isEdited) return 'Late (Edited)'

  return 'Late'
}

const getCheckinBadge = (status: string) => {
  switch (status) {
    case 'On-time':
      return {
        color: 'success' as const,
        label: 'On-time',
      }
    case 'Late':
      return {
        color: 'warning' as const,
        label: 'Late',
      }
    case 'On-time (Edited)':
      return {
        color: 'success' as const,
        label: 'On-time (Edited)',
      }
    case 'Late (Edited)':
      return {
        color: 'warning' as const,
        label: 'Late (Edited)',
      }
    default:
      return {
        color: 'neutral' as const,
        label: 'Unknown',
      }
  }
}

// const clearFilters = () => {
//   form.setFieldValue('team', undefined)
//   form.setFieldValue('type', undefined)
//   form.setFieldValue('search', undefined)
// }

const fetchCheckins = async () => {
  const params = {
    select: 'date,created_at,user_id,checkin_type,original_timestamp,leave_period,location,is_unused,remark,profiles(display_name,team,email)',
    date: `gte.${startDate.value}&date=lte.${endDate.value}`,
    order: 'date.desc',
  }

  await checkinLoader.fetchPage(1, '', {
    params: {
      ...params,
    },
  })
}

watch([form.values.date], () => {
  fetchCheckins()
})
</script>

<style scoped>
::v-deep(.custom-datepicker .dp__input) {
  height: 32px !important;
}
</style>
