import { LEAVE_TYPE, LEAVE_LABEL, type LeaveType } from './leave_types'

export enum CHECKIN {
  OFFICE_HQ = 'OFFICE_HQ',
  OFFICE_AKV = 'OFFICE_AKV',
  WFH = 'WFH',
  ONSITE = 'onsite',
}

export type CheckinType = CHECKIN | LeaveType

export const CHECKIN_LABEL: Record<CheckinType, string> = {
  [CHECKIN.OFFICE_HQ]: 'Office HQ',
  [CHECKIN.OFFICE_AKV]: 'Office AKV',
  [CHECKIN.WFH]: 'WFH',
  [CHECKIN.ONSITE]: 'Onsite',
  ...LEAVE_LABEL,
}

export const CHECKIN_TYPES = [
  CHECKIN.OFFICE_HQ,
  CHECKIN.OFFICE_AKV,
  CHECKIN.WFH,
  CHECKIN.ONSITE,
  ...Object.values(LEAVE_TYPE),
] as const

export const CHECKIN_COLORS: Record<string, string> = {
  'Annual Leave': '#17B26A',
  'Sick Leave': '#F79009',
  'Onsite': '#F04438',
  'Office HQ': '#2E90FA',
  'Office AKV': '#84CAFF',
  'WFH': '#8B5CF6',
  'Menstrual Leave': '#FDA29B',
  'Business Leave': '#1849A9',
  'Birthday Leave': '#FEDF89',
  'Ordination Leave': '#FF5733',
}

export const CHECKIN_OPTIONS = CHECKIN_TYPES.map((type) => ({
  label: CHECKIN_LABEL[type],
  value: type,
}))
