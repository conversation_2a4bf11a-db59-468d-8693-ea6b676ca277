export const enum TRACKER_TYPE {
  PROJECT = 'project',
  SGA = 'sga',
  LEAVE = 'leave',
  INTERNAL = 'internal',
  EXTERNAL = 'external',
  OT = 'ot',
}

export const TRACKER_TYPE_LABEL = {
  [TRACKER_TYPE.PROJECT]: 'On Project',
  [TRACKER_TYPE.SGA]: 'SGA',
  [TRACKER_TYPE.LEAVE]: 'Leave',
  [TRACKER_TYPE.INTERNAL]: 'Internal',
  [TRACKER_TYPE.EXTERNAL]: 'External',
  [TRACKER_TYPE.OT]: 'OT',
}

export const TRACKER_TYPE_OPTIONS = [
  {
    label: TRACKER_TYPE_LABEL[TRACKER_TYPE.PROJECT],
    value: TRACKER_TYPE.PROJECT,
  },
  {
    label: TRACKER_TYPE_LABEL[TRACKER_TYPE.SGA],
    value: TRACKER_TYPE.SGA,
  },
  {
    label: TRACKER_TYPE_LABEL[TRACKER_TYPE.LEAVE],
    value: TRACKER_TYPE.LEAVE,
  },
  {
    label: TRACKER_TYPE_LABEL[TRACKER_TYPE.INTERNAL],
    value: TRACKER_TYPE.INTERNAL,
  },
  {
    label: TRACKER_TYPE_LABEL[TRACKER_TYPE.EXTERNAL],
    value: TRACKER_TYPE.EXTERNAL,
  },
  {
    label: TRACKER_TYPE_LABEL[TRACKER_TYPE.OT],
    value: TRACKER_TYPE.OT,
  },
]
